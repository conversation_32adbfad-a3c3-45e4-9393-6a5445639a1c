# Transition from Class-Based to Functional Approach for Review Processing

## Executive Summary

This document outlines a comprehensive strategy for transitioning the review processing codebase from a class-based (OOP) approach to a functional programming paradigm. The transition will improve modularity, testability, and maintainability while preserving all existing functionality.

## Current Architecture Analysis

### Class Hierarchy
```
BaseReviewProcessor (Abstract Base Class)
├── SalesReviewProcessor
├── ProductReviewProcessor
└── EmployeeReviewProcessor
```

### Current Responsibilities

**BaseReviewProcessor:**
- Review preprocessing and formatting
- Review categorization using LLM
- JSON parsing and validation
- Review ID extraction
- Abstract methods for specialized processing

**SalesReviewProcessor:**
- Sales-specific database collections
- Review chunk storage with ObjectId handling
- Category-based summarization
- Insights aggregation and flagging
- Ticker-based processing

## Proposed Functional Architecture

### Core Design Principles

1. **Pure Functions**: Functions should have no side effects and return consistent outputs for given inputs
2. **Composition**: Build complex operations by composing simple functions
3. **Separation of Concerns**: Each function handles a single responsibility
4. **Data Pipeline**: Process data through a series of transformations
5. **Dependency Injection**: Pass dependencies explicitly rather than storing in state

### Module Structure

```
src/reviews/
├── core/
│   ├── preprocessing.py      # Text preprocessing functions
│   ├── categorization.py     # LLM categorization logic
│   ├── parsing.py           # JSON parsing and validation
│   └── extraction.py        # ID and data extraction
├── processors/
│   ├── sales.py             # Sales-specific processing
│   ├── products.py          # Product-specific processing
│   └── employees.py         # Employee-specific processing
├── storage/
│   ├── chunks.py            # Review chunk storage
│   ├── insights.py          # Insights storage
│   └── aggregation.py       # Data aggregation
├── pipelines/
│   ├── sales_pipeline.py    # Sales processing pipeline
│   ├── products_pipeline.py # Products processing pipeline
│   └── employees_pipeline.py # Employees processing pipeline
├── schemas.py               # Data models (unchanged)
├── prompts.py              # Prompt templates (unchanged)
└── config.py               # Configuration and constants
```

## Functional Transformation Patterns

### 1. State Management → Function Parameters

**Current (Class-based):**
```python
class BaseReviewProcessor:
    def __init__(self, cutoff_date: datetime, end_date: datetime):
        self.CUTOFF_DATE = cutoff_date
        self.END_DATE = end_date
```

**New (Functional):**
```python
def process_reviews(reviews: List[dict], cutoff_date: datetime, end_date: datetime) -> List[dict]:
    filtered_reviews = filter_by_date(reviews, cutoff_date, end_date)
    return filtered_reviews
```

### 2. Method Chaining → Function Composition

**Current:**
```python
class SalesReviewProcessor(BaseReviewProcessor):
    def run(self):
        for doc in self.ticker_collection.find():
            self._process_one_ticker(ticker, slug)
```

**New:**
```python
def process_sales_reviews(
    ticker_collection: Collection,
    cutoff_date: datetime,
    end_date: datetime
) -> List[dict]:
    return pipe(
        ticker_collection.find(),
        partial(map, lambda doc: process_ticker(doc, cutoff_date, end_date)),
        list
    )
```

### 3. Instance Methods → Pure Functions

**Current:**
```python
def _preprocess_reviews(self, query: dict, collection: Collection, field_name: str = "content", id_prefix: str = "") -> str:
    # Method implementation
```

**New:**
```python
def preprocess_reviews(
    query: dict,
    collection: Collection,
    field_name: str = "content",
    id_prefix: str = ""
) -> str:
    # Pure function implementation
```

## Implementation Strategy

### Phase 1: Extract Pure Functions (Week 1)
1. Create `core/` modules with pure functions
2. Extract all data transformation logic
3. Remove dependencies on instance state
4. Add comprehensive unit tests

### Phase 2: Create Pipelines (Week 2)
1. Design data pipelines for each processor type
2. Implement function composition utilities
3. Create pipeline orchestration functions
4. Test end-to-end flows

### Phase 3: Refactor Storage Operations (Week 3)
1. Convert storage methods to functions
2. Implement connection pooling
3. Add retry logic and error handling
4. Create storage abstraction layer

### Phase 4: Migration and Testing (Week 4)
1. Parallel run both implementations
2. Compare outputs for validation
3. Performance benchmarking
4. Gradual cutover strategy

## Key Benefits

### 1. Improved Testability
- Pure functions are easier to test in isolation
- No need to mock class state or dependencies
- Predictable inputs and outputs

### 2. Enhanced Modularity
- Functions can be reused across different contexts
- Easy to compose new pipelines
- Clear separation of concerns

### 3. Better Maintainability
- Simpler to understand individual functions
- Reduced coupling between components
- Easier to debug and trace data flow

### 4. Increased Flexibility
- Functions can be composed in different ways
- Easy to add new processing steps
- Simple to modify existing pipelines

## Code Examples

### Example 1: Review Preprocessing

**Functional approach:**
```python
# src/reviews/core/preprocessing.py
from typing import List, Dict
from pymongo.collection import Collection

def format_review(review: Dict, field_name: str, id_prefix: str = "") -> str:
    """Format a single review into standardized text."""
    try:
        return f"review_id: {id_prefix}{review['_id']} review_{field_name}: {review[field_name]} "
    except KeyError as e:
        print(f"KeyError in {review}: {e}")
        return ""

def preprocess_reviews(
    query: Dict,
    collection: Collection,
    field_name: str = "content",
    id_prefix: str = ""
) -> str:
    """Format raw reviews into standardized text strings with review IDs."""
    if collection.count_documents(query) == 0:
        return ""
    
    reviews = collection.find(query)
    return "".join(format_review(review, field_name, id_prefix) for review in reviews)
```

### Example 2: Sales Pipeline

**Functional pipeline:**
```python
# src/reviews/pipelines/sales_pipeline.py
from functools import partial
from typing import Dict, List
from datetime import datetime

def create_sales_pipeline(
    cutoff_date: datetime,
    end_date: datetime,
    openai_service: OpenAIService
):
    """Create a sales review processing pipeline."""
    return compose(
        partial(fetch_tickers),
        partial(map, lambda ticker: {
            'ticker': ticker,
            'reviews': fetch_reviews(ticker, cutoff_date, end_date)
        }),
        partial(map, lambda data: {
            **data,
            'preprocessed': preprocess_reviews(data['reviews'])
        }),
        partial(map, lambda data: {
            **data,
            'categorized': categorize_reviews(
                data['preprocessed'],
                SALES_REVIEWS_CATEGORIZATION_PROMPT,
                openai_service
            )
        }),
        partial(map, store_review_chunks),
        partial(map, generate_insights),
        list
    )
```

## Migration Checklist

- [ ] Extract all pure functions from base class
- [ ] Create functional modules for each responsibility
- [ ] Design pipeline architecture
- [ ] Implement sales processor functions
- [ ] Implement product processor functions
- [ ] Implement employee processor functions
- [ ] Create integration tests
- [ ] Performance benchmarking
- [ ] Documentation update
- [ ] Team training on functional approach

## Conclusion

The transition to a functional approach will result in cleaner, more maintainable code that's easier to test and extend. The modular design will allow for better code reuse and make it simpler to add new review processing types in the future.