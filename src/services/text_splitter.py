import spacy
from typing import List


class TextSplitter:
    def __init__(self, words_per_chunk: int = 400, model: str = "en_core_web_sm"):
        """
        Initialize the TextSplitter.

        Args:
            words_per_chunk (int): Maximum number of words in each chunk. Defaults to 400.
            model (str): Which spaCy model to use for tokenization. Defaults to "en_core_web_sm".
        """
        self.words_per_chunk = words_per_chunk
        self.nlp = spacy.load(model)

    def _split(self, text):
        """
        Split the input text into chunks of specified word count.

        Args:
            text (str): The input text to be split into chunks.

        Returns:
            list: An array of string chunks, each containing up to the specified number of words.
        """
        if len(text) > 1000000:
            self.nlp.disable_pipes(["tagger", "ner"])
        doc = self.nlp(text)
        sentences = [sent.text.strip() for sent in doc.sents]
        return sentences

    def _combine_short_consecutive_sentences(self, sentences: List[str]) -> List[str]:
        """
        Combine consecutive sentences if their combined word count is less than max_words.

        Args:
            sentences (List[str]): List of sentences to process

        Returns:
            List[str]: A list of sentences after combining short consecutive sentences
        """
        if not sentences:
            return []

        sentences = [s for s in sentences if s.strip()]

        if not sentences:
            return []

        result = [sentences[0]]

        for current_sentence in sentences[1:]:
            last_sentence = result[-1]

            last_word_count = len(last_sentence.split())
            current_word_count = len(current_sentence.split())
            combined_word_count = last_word_count + current_word_count

            if combined_word_count <= self.words_per_chunk:
                result[-1] = f"{last_sentence} {current_sentence}"
            else:
                result.append(current_sentence)

        return result

    def split(self, text):
        """
        Split the input text into chunks of specified word count.

        Args:
            text (str): The input text to be split into chunks.

        Returns:
            list: An array of string chunks, each containing up to the specified number of words.
        """
        sentences = self._split(text)
        combined_sentences = self._combine_short_consecutive_sentences(sentences)
        return combined_sentences

    def set_words_per_chunk(self, words_per_chunk):
        """
        Update the number of words per chunk.

        Args:
            words_per_chunk (int): New maximum number of words in each chunk.
        """
        if words_per_chunk <= 0:
            raise ValueError("Words per chunk must be a positive integer")
        self.words_per_chunk = words_per_chunk
