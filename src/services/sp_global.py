import requests
from threading import Lock
from src.core.logging import get_logger
from src.core.constants import SP_USERNAME, SP_PASSWORD


logger = get_logger(__name__)


class SPGlobalClient:
    _instance = None
    _lock = Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SPGlobalClient, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self._token = None
        self._auth_url = "https://api-ciq.marketintelligence.spglobal.com/gdsapi/rest/authenticate/api/v1/token"
        self._search_url = "https://api-ciq.marketintelligence.spglobal.com/gds/documents/api/v1/search?docType=TRANSCRIPTS_DOCUMENTS_API"
        self._download_url = "https://api-ciq.marketintelligence.spglobal.com/gds/documents/api/v1/download?docType=TRANSCRIPTS_DOCUMENTS_API"
        self._initialized = True

    def _get_token(self):
        """Get a valid authentication token, refreshing if necessary"""
        auth_headers = {
            "accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        auth_data = {
            "username": SP_USERNAME,
            "password": SP_PASSWORD
        }

        try:
            auth_response = requests.post(self._auth_url, headers=auth_headers, data=auth_data)
            auth_response.raise_for_status()
            self._token = auth_response.json().get("access_token")
            if not self._token:
                raise ValueError("Authentication successful but no access token received.")
            return self._token

        except Exception as e:
            logger.info(f"Authentication failed: {e}")
            self._token = None
            raise

    def download_from_sp(self, unique_event_id):
        """Download transcript document from SP Global using the unique event ID"""
        try:
            token = self._get_token()
            search_headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            search_data = {
                "properties": {
                    "ciqKeyDevId": [unique_event_id],
                    "documentFormatTypeId": [7]
                }
            }

            response = requests.post(self._search_url, headers=search_headers, json=search_data)
            response.raise_for_status()
            response_data = response.json()

            document_id_index = response_data['headers'].index('transcriptDocumentId')
            document_id = response_data['rows'][0]['row'][document_id_index]
            logger.info(f"Search successful. Document ID: {document_id}")

            download_headers = {
                "accept": "*/*",
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            download_data = {
                "properties": {
                    "transcriptDocumentId": document_id,
                    "documentFormatTypeId": 7
                }
            }

            data_response = requests.post(self._download_url, headers=download_headers, json=download_data)
            data_response.raise_for_status()
            logger.info("Transcript document downloaded successfully")
            return data_response.content

        except Exception as e:
            logger.info(f"Error in SP Global operation: {e}")
            return None


# Example usage:
# client = SPGlobalClient()
# transcript = client.download_from_sp("your_unique_event_id")
