from sec_api import Query<PERSON><PERSON>, Xbrl<PERSON>pi
from src.core.constants import SEC_API_KEY


def get_quarterly_revenue(ticker):
    """
    Extract quarterly revenue for a company from their 10-Q filing.

    Parameters:
    ticker (str): The company's ticker symbol (e.g., 'MSFT')
    api_key (str): Your SEC-API API key

    Returns:
    float: Revenue amount in millions/billions (as reported)
    """
    queryApi = QueryApi(api_key=SEC_API_KEY)
    xbrlApi = XbrlApi(api_key=SEC_API_KEY)

    query = {
        "query": f'formType:"10-Q" AND ticker:{ticker}',
        "from": "0",
        "size": "2",
        "sort": [{"filedAt": {"order": "desc"}}]
    }

    # Execute the query to find the filing
    response = queryApi.get_filings(query)

    # Check if a filing was found
    if len(response['filings']) == 0:
        return f"No 10-Q filing found for {ticker}"

    # Get the first filing (most recent if multiple exist)
    filing = response['filings'][0]
    accession_no = filing['accessionNo']
    # Use the XBRL API to extract financial data
    xbrl_data = xbrlApi.xbrl_to_json(accession_no=accession_no)
    return xbrl_data
