import logging
from openai import OpenAI
from openai._types import NOT_GIVEN
from src.core.logging import get_logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log
from src.core.constants import GEMINI_API_KEY


logger = get_logger(__name__)


class GeminiService:
    """
    Singleton class to manage OpenAI Gemini client connections.
    Ensures a single connection is maintained and reused across the application.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GeminiService, cls).__new__(cls)
            cls._instance._client = None
        return cls._instance

    def get_client(self):
        """
        Get or create an OpenAI Gemini client using the API key.

        Returns:
            openai.Client: Configured OpenAI client
        """
        if self._client is None:
            try:
                self._client = OpenAI(
                    api_key=GEMINI_API_KEY,
                    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
                )
                logger.info("Successfully created OpenAI client")
            except Exception as e:
                logger.error(f"Failed to create OpenAI client: {str(e)}")
                raise
        return self._client

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )
    def get_completion(
        self,
        prompt,
        model="gemini-2.5-flash-preview-04-17",
        system_prompt="You are a helpful analyst",
        max_token=500,
        temperature=0.5,
        response_format=NOT_GIVEN,
    ):
        client = OpenAI(
            api_key=GEMINI_API_KEY,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt},
            ],
            max_tokens=max_token,
            response_format=response_format,
            temperature=temperature,
        )
        parsed_response = response.choices[0].message.content.strip()
        return parsed_response

    def get_completion_without_limits(
        self,
        prompt,
        model="gemini-2.5-flash-preview-04-17",
        temperature=0.5,
        response_format=NOT_GIVEN,
    ):
        client = OpenAI(
            api_key=GEMINI_API_KEY,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt},
            ],
            response_format=response_format,
            temperature=temperature,
        )
        parsed_response = response.choices[0].message.content.strip()
        return parsed_response
