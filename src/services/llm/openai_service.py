import json
from openai import BadRequest<PERSON>rror
import logging
from openai import <PERSON>zure<PERSON>penAI
from openai._types import NOT_GIVEN
from src.core.logging import get_logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log
from src.core.constants import OPENAI_AZURE_ENDPOINT_EAST_US, OPENAI_AZURE_KEY_EAST_US, OPENAI_AZURE_API_VERSION_EAST_US


logger = get_logger(__name__)


class OpenAIService:
    """
    Singleton class to manage OpenAI client connections.
    Ensures a single connection is maintained and reused across the application.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OpenAIService, cls).__new__(cls)
            cls._instance._client = None
        return cls._instance

    def get_client(self):
        """
        Get or create an OpenAI client using the API key.

        Returns:
            openai.Client: Configured OpenAI client
        """
        if self._client is None:
            try:
                self._client = AzureOpenAI(
                    azure_endpoint=OPENAI_AZURE_ENDPOINT_EAST_US,
                    api_version=OPENAI_AZURE_API_VERSION_EAST_US,
                    api_key=OPENAI_AZURE_KEY_EAST_US
                )
                logger.info("Successfully created OpenAI client")
            except Exception as e:
                logger.error(f"Failed to create OpenAI client: {str(e)}")
                raise
        return self._client

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )
    def get_completion(
        self,
        prompt,
        model="gpt-4o",
        system_prompt="You are a helpful analyst",
        max_token=500,
        temperature=0.0,
        response_format=NOT_GIVEN,
    ):
        try:
            client = AzureOpenAI(
                azure_endpoint=OPENAI_AZURE_ENDPOINT_EAST_US,
                api_version=OPENAI_AZURE_API_VERSION_EAST_US,
                api_key=OPENAI_AZURE_KEY_EAST_US
            )
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt},
                ],
                max_tokens=max_token,
                response_format=response_format,
                temperature=temperature,
            )
            if response.choices[0].finish_reason == "content_filter":
                return ""
            parsed_response = response.choices[0].message.content.strip()
            return parsed_response
        except BadRequestError as e:
            logger.error(e)
            if response_format == {'type': 'json_object'}:
                return json.dumps({})
            return ""

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=5, max=60),
        retry=retry_if_exception_type(Exception),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        reraise=True
    )
    def get_completion_without_limits(
        self,
        prompt,
        model="gpt-4o",
        temperature=0.0,
        response_format=NOT_GIVEN,
    ):
        try:
            client = AzureOpenAI(
                azure_endpoint=OPENAI_AZURE_ENDPOINT_EAST_US,
                api_version=OPENAI_AZURE_API_VERSION_EAST_US,
                api_key=OPENAI_AZURE_KEY_EAST_US
            )
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                ],
                response_format=response_format,
                temperature=temperature,
            )
            if response.choices[0].finish_reason == "content_filter":
                return ""
            parsed_response = response.choices[0].message.content.strip()
            return parsed_response
        except BadRequestError as e:
            logger.error(e)
            if response_format == {'type': 'json_object'}:
                return json.dumps({})
            return ""
