import httpx
from deepgram import Deepgram<PERSON>lient, PrerecordedOptions

from src.core.logging import get_logger
from src.core.constants import DEEPGRAM_API_KEY

logger = get_logger(__name__)


def transcribe(audio_url: str):
    try:
        deepgram = DeepgramClient(api_key=DEEPGRAM_API_KEY)

        options = PrerecordedOptions(
            model="nova-3",
            diarize=True,
            detect_entities=True,
            smart_format=True,
        )

        response = deepgram.listen.rest.v("1").transcribe_url(
            {"url": audio_url}, options, timeout=httpx.Timeout(1800.0, connect=10.0)
        )

        # print(response.to_json(indent=4))
        return response.to_dict()

    except Exception as e:
        print(f"Exception: {e}")
