COMPANY_INFO_BY_TICKER_SYMBOL = """
    SELECT
        c.companyName,
        c.companyId,
        ti.tickerSymbol,
        e.exchangeSymbol,
        st.companyStatusTypeName,
        ts.tradingItemStatusName
    FROM
        ciqCompany c
    JOIN
        ciqSecurity s ON c.companyId = s.companyId
    JOIN
        ciqTradingItem ti ON ti.securityId = s.securityId
    JOIN
        ciqExchange e ON e.exchangeId = ti.exchangeId
    JOIN
        ciqCompanyStatusType st ON st.companyStatusTypeId = c.companyStatusTypeId
    JOIN
        ciqTradingItemStatus ts ON ts.tradingItemStatusId = ti.tradingItemStatusId
    WHERE
        (e.exchangeSymbol LIKE 'NasdaqG%' OR e.exchangeSymbol = 'NYSE')
        AND ti.tickerSymbol = '{}'
        AND s.primaryFlag = 1
        AND ti.tradingItemStatusId = 15
        AND s.securityEndDate IS NULL"""

LATEST_COMPANY_TRANSCRIPTS = """
WITH RankedTranscripts AS (
    SELECT
        C.COMPANYID,
        C.COMPANYNAME,
        T.TRANSCRIPTCREATIONDATEUTC,
        T.TRANSCRIPTID,
        T.KEYDEVID,
        E.MOSTIMPORTANTDATEUTC,
        E.HEADLINE,
        E.LASTMODIFIEDDATE,
        ET.KEYDEVEVENTTYPENAME,
        TCT.TRANSCRIPTCOLLECTIONTYPENAME,
        TPT.TRANSCRIPTPRESENTATIONTYPENAME,
        ROW_NUMBER() OVER (
            PARTITION BY T.KEYDEVID
            ORDER BY
                T.TRANSCRIPTCREATIONDATEUTC DESC
        ) AS rn
    FROM
        CIQTRANSCRIPT T
        INNER JOIN CIQEVENTPIT E ON E.KEYDEVID = T.KEYDEVID
        INNER JOIN CIQEVENTTOOBJECTTOEVENTTYPEPIT ETE ON ETE.KEYDEVID = T.KEYDEVID
        INNER JOIN CIQEVENTTYPE ET ON ET.KEYDEVEVENTTYPEID = ETE.KEYDEVEVENTTYPEID
        INNER JOIN CIQCOMPANY C ON C.COMPANYID = ETE.OBJECTID
        INNER JOIN CIQTRANSCRIPTPRESENTATIONTYPE TPT ON TPT.TRANSCRIPTPRESENTATIONTYPEID = T.TRANSCRIPTPRESENTATIONTYPEID
        INNER JOIN CIQTRANSCRIPTCOLLECTIONTYPE TCT ON TCT.TRANSCRIPTCOLLECTIONTYPEID = T.TRANSCRIPTCOLLECTIONTYPEID
    WHERE
        C.COMPANYID IN (
            SELECT
                VALUE
            FROM
                TABLE(SPLIT_TO_TABLE({snow_id_pooled}))
        )
        AND T.TRANSCRIPTPRESENTATIONTYPEID IN (4, 5) -- for Interim and Final do in (4.5)
        AND E.MOSTIMPORTANTDATEUTC BETWEEN '{cutoff_date_start}'
        AND '{cutoff_date_end}'
)
SELECT
    COMPANYID,
    COMPANYNAME,
    TRANSCRIPTCREATIONDATEUTC,
    TRANSCRIPTID,
    KEYDEVID,
    MOSTIMPORTANTDATEUTC,
    HEADLINE,
    LASTMODIFIEDDATE,
    KEYDEVEVENTTYPENAME,
    TRANSCRIPTCOLLECTIONTYPENAME,
    TRANSCRIPTPRESENTATIONTYPENAME
FROM
    RankedTranscripts
WHERE
    rn = 1 -- Select only the most recent entry per KEYDEVID
ORDER BY
    LASTMODIFIEDDATE DESC;
"""

TRANSCRIPT_PARTICIPANT_DETAILS = """
    SELECT DISTINCT
        tp.transcriptpersonname AS NAME,
        st.speakertypename AS SPEAKERTYPE,
        p.title AS TITLE,
        c2.companyname AS COMPANYNAME
    FROM
        ciqTranscript t
    JOIN
        ciqEventPIT e ON e.keyDevId = t.keyDevId
    JOIN
        ciqEventToObjectToEventTypePIT ete ON ete.keyDevId = t.keyDevId
    JOIN
        ciqCompany c ON c.companyId = ete.objectId
    JOIN
        ciqTranscriptComponent tc ON tc.transcriptId = t.transcriptId
    JOIN
        ciqTranscriptPerson tp ON tp.transcriptPersonId = tc.transcriptPersonId
    JOIN
        ciqTranscriptSpeakerType st ON st.speakerTypeId = tp.speakerTypeId
    LEFT JOIN
        ciqProfessional p ON p.proid = tp.proid
    LEFT JOIN
        ciqCompany c2 ON c2.companyid = p.companyid
    WHERE
        t.transcriptId = '{}'
    ORDER BY
        tp.transcriptpersonname;
"""

TRANSCRIPT_COMPANY_COMPONENT_DETAILS_QUERY = """
    SELECT
        C.companyid,
        C.companyname,
        TC.componentText,
        PE.firstName,
        PE.lastName,
        P.title,
        TCTY.transcriptComponentTypeName,
    FROM
        CIQTRANSCRIPT T
    JOIN
        CIQEVENTPIT e ON e.keyDevId = t.keyDevId
    JOIN
        CIQEVENTTOOBJECTTOEVENTTYPEPIT ETE ON ETE.keyDevId = T.keyDevId
    JOIN
        CIQEVENTTYPE ET ON ET.keyDevEventTypeId = ETE.keyDevEventTypeId
    JOIN
        CIQCOMPANY C ON C.companyId = ETE.objectId
    JOIN
        CIQTRANSCRIPTCOMPONENT TC ON TC.transcriptid = T.transcriptId
    JOIN
        CIQTRANSCRIPTCOMPONENTTYPE TCTY ON TCTY.transcriptcomponenttypeid = TC.transcriptComponentTypeId
    JOIN
        CIQTRANSCRIPTPERSON TP ON TP.transcriptPersonId = TC.transcriptPersonId
    JOIN
        CIQPROFESSIONAL P ON P.proId = TP.proId
    JOIN
        CIQPERSON PE ON PE.personId = P.personId
    WHERE
        T.transcriptId = '{}'
    AND
        E.spToDate IS null
    ORDER BY
        TC.componentorder;
"""

UPCOMING_EARNING_CALLS_QUERY = """
    SELECT
        e.mostimportantdateutc AS mostimportantdateutc_event,
        comp.companyId AS company_id,
        comp.companyName AS company_name,
        e.keyDevId AS keyDevId_event,
        ete.keyDevId AS keyDevId_object,
        ete.keyDevEventTypeId,
        eort.keyDevToObjectRoleTypeId,
        et.keyDevEventTypeName AS event_type,
        e.announcedDate AS event_announced_date

    FROM ciqEventPIT e

    JOIN ciqEventToObjectToEventTypePIT ete ON ete.keyDevId = e.keyDevId
    JOIN ciqEventType et ON et.keyDevEventTypeId = ete.keyDevEventTypeId
    JOIN ciqEventObjectRoleType eort ON eort.keyDevToObjectRoleTypeId = ete.keyDevToObjectRoleTypeId
    JOIN ciqCompany comp ON comp.companyId = ete.objectId

    WHERE 1=1
    AND comp.companyid IN ('{company_id}')
    AND e.mostImportantDateUTC > getdate()
    AND et.keyDevEventTypeName = 'Earnings Calls'

    ORDER BY e.mostImportantDateUTC
"""

GET_COMPANY_EMPLOYEES_QUERY = """
    WITH CompanyList AS (
        SELECT *
        FROM (VALUES
            ({company_id})
            /* ,(more-company-ids) */
        ) v(COMPANYID)
    )
    /* ---------------------------------------------
    2)  Main query
    ---------------------------------------------*/
    SELECT DISTINCT
        p.PERSONID,
        p.FIRSTNAME,
        p.MIDDLENAME,
        p.LASTNAME,
        /* Full name in one column, skips null middle names */
        CONCAT_WS(' ',
                    p.FIRSTNAME,
                    NULLIF(p.MIDDLENAME,''),
                    p.LASTNAME)                    AS PersonName,
        c.COMPANYID,
        c.COMPANYNAME,
        pr.TITLE
    FROM       CompanyList        cl
    INNER JOIN ciqPROFESSIONAL    pr ON pr.COMPANYID = cl.COMPANYID
    INNER JOIN ciqPERSON          p  ON p.PERSONID   = pr.PERSONID
    INNER JOIN ciqCOMPANY         c  ON c.COMPANYID  = pr.COMPANYID
    /* optional filters
    WHERE pr.CURRENTPROFLAG = 1          -- only current roles
    ORDER BY c.COMPANYNAME, PersonName
    */
    ;
"""
