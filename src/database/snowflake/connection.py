import snowflake.connector

from src.core.logging import get_logger
from src.core.constants import SNOW_USER, SNOW_WAREHOUSE, SNOW_ACCOUNT, SNOW_DATABASE, SNOW_PASSWORD, SNOW_SCHEMA


logger = get_logger(__name__)


class SnowflakeConnection:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.connection = None
        return cls._instance

    def connect(self):
        if self.connection is None or self.connection.is_closed():
            try:
                self.connection = snowflake.connector.connect(
                    user=SNOW_USER,
                    password=SNOW_PASSWORD,
                    account=SNOW_ACCOUNT,
                    warehouse=SNOW_WAREHOUSE,
                    database=SNOW_DATABASE,
                    schema=SNOW_SCHEMA,
                    client_session_keep_alive=True,
                    insecure_mode=True,
                )
                logger.info("Connected to Snowflake successfully.")
            except Exception as e:
                logger.error(f"Error connecting to Snowflake: {e}")
                raise

    def get_connection(self):
        if self.connection is None or self.connection.is_closed():
            self.connect()
        return self.connection

    def close_connection(self):
        if self.connection:
            self.connection.close()
            logger.info("Snowflake connection closed.")

    def execute_query(self, query, params=None):
        try:
            cursor = self.get_connection().cursor()
            cursor.execute(query, params)
            return cursor
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            raise
