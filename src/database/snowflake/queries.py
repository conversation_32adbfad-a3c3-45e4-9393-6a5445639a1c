import pandas as pd
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.database.snowflake.query_definitions import COMPANY_INFO_BY_TICKER_SYMBOL, LATEST_COMPANY_TRANSCRIPTS, TRANSCRIPT_PARTICIPANT_DETAILS, TRANSCRIPT_COMPANY_COMPONENT_DETAILS_QUERY, UPCOMING_EARNING_CALLS_QUERY, GET_COMPANY_EMPLOYEES_QUERY

logger = get_logger(__name__)


def extract_company_by_id(ticker: str):
    """
    Extracts company ID for a given ticker symbol using the SnowflakeConnection.

    Args:
        ticker (str): The ticker symbol to search for

    Returns:
        int or None: The company ID if found, None otherwise
    """
    try:
        connection_manager = DatabaseFactory().get_snowflake_connection()
        query = COMPANY_INFO_BY_TICKER_SYMBOL.format(ticker)
        cursor = connection_manager.execute_query(query)
        row = cursor.fetchone()
        if not row:
            logger.info("No data found")
            return None
        company_name = row[0]
        company_id = row[1]
        logger.info(f"Fetched Information:\nTicker: {ticker}, Company Name: {company_name}, Company ID: {company_id}")
        return company_id

    except Exception as e:
        logger.error(f"Error extracting company ID for ticker {ticker}: {e}")
        return None


def get_latest_company_transcript(snow_ids, cutoff_date_start, cutoff_date_end):
    """
    Retrieves the latest transcripts for a company within a specified date range.

    Args:
        snow_id (int): Company identifier in Snowflake database
        cutoff_date_start (str): Starting date for transcript search
        cutoff_date_end (str): Ending date for transcript search

    Returns:
        pandas.DataFrame: DataFrame containing company transcript data
    """
    snow_id_pooled = ", ".join(f"{snow_id}" for snow_id in snow_ids)
    snow_id_pooled = f"'{snow_id_pooled}', ','"
    query = LATEST_COMPANY_TRANSCRIPTS.format(
        snow_id_pooled=snow_id_pooled,
        cutoff_date_start=cutoff_date_start,
        cutoff_date_end=cutoff_date_end
    )
    connection_manager = DatabaseFactory().get_snowflake_connection()
    connection = connection_manager.get_connection()
    df = pd.read_sql_query(query, connection)
    return df


def snowflake_extract_participants(transcriptid):
    """
    Extracts participant information from a transcript.

    Args:
        transcriptid: Unique identifier for the transcript

    Returns:
        tuple: (corporate_reps, analysts) where each is a list of tuples 
               containing (name, title, affiliation)
    """
    query = TRANSCRIPT_PARTICIPANT_DETAILS.format(transcriptid)
    connection_manager = DatabaseFactory().get_snowflake_connection()
    connection = connection_manager.get_connection()
    df = pd.read_sql_query(query, connection)
    corporate_reps = []
    analysts = []
    for _, row in df.iterrows():
        if row['SPEAKERTYPE'] == 'Executives':
            name = row['NAME']
            title = row['TITLE']
            affiliation = row['COMPANYNAME']
            corporate_reps.append((name, title, affiliation))
        elif row['SPEAKERTYPE'] == 'Analysts':
            name = row['NAME']
            title = row['TITLE']
            affiliation = row['COMPANYNAME']
            analysts.append((name, title, affiliation))
    return corporate_reps, analysts


def fetch_transcript_data(transcript_id):
    query = TRANSCRIPT_COMPANY_COMPONENT_DETAILS_QUERY.format(transcript_id)
    connection_manager = DatabaseFactory().get_snowflake_connection()
    connection = connection_manager.get_connection()
    df = pd.read_sql_query(query, connection)
    logger.info("Snowflake read successful")
    return df


def get_upcoming_earning_calls(company_id):
    query = UPCOMING_EARNING_CALLS_QUERY.format(company_id=company_id)
    connection_manager = DatabaseFactory().get_snowflake_connection()
    connection = connection_manager.get_connection()
    df = pd.read_sql_query(query, connection)
    return df


def get_company_employees(company_id):
    query = GET_COMPANY_EMPLOYEES_QUERY.format(company_id=company_id)
    connection_manager = DatabaseFactory().get_snowflake_connection()
    connection = connection_manager.get_connection()
    df = pd.read_sql_query(query, connection)
    return df
