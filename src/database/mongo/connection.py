import time
import threading
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, AutoReconnect

from src.core.logging import get_logger
from src.core.constants import ( B<PERSON><PERSON><PERSON>BERRY_SALES_DB_NAME, BLACKBERRY_EMPLOYEE_DB_NAME, BLACKBERRY_PRODUCT_DB_NAME,
                                    MONGO_URI, MONGO_BLACKBERRY_URI, MONGO_DB_NAME, MONGO_MAX_POOL_SIZE, MONGO_MIN_POOL_SIZE, 
                                    MONGO_MAX_IDLE_TIME_MS, MONGO_STOCK_DB_NAME, MONGO_EMAIL_DB_NAME, PODCAST_DB_NAME, SALES_INSIGHT_DB_NAME, 
                                    PRODUCT_INSIGHT_DB_NAME, EMPLOYEE_INSIGHT_DB_NAME, FILINGS_DB_NAME, FEED_DB_NAME )


logger = get_logger(__name__)


class MongoDBConnection:
    _instance = None
    _lock = threading.Lock()  # For thread safety in singleton pattern

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(MongoDBConnection, cls).__new__(cls)
                cls._instance.blackberry_client = None
                cls._instance.blackberry_sales_db = None
                cls._instance.blackberry_employee_db = None
                cls._instance.blackberry_product_db = None
                cls._instance.client = None
                cls._instance.db = None
                cls._instance.stock_db = None
                cls._instance.podcast_db = None
                cls._instance.email_db = None
                cls._instance.sales_db = None
                cls._instance.product_insights_db = None
                cls._instance.employee_insights_db = None
                cls._instance.filings_db = None
                cls._instance.feed_db = None
                cls._instance.healthy = False
                cls._instance._connect()
            return cls._instance

    def _connect(self, max_retries=3, retry_delay=2):
        """Connect to MongoDB with retries"""
        mongo_blackberry_uri = MONGO_BLACKBERRY_URI
        blackberry_sales_db_name = BLACKBERRY_SALES_DB_NAME
        blackberry_employee_db_name = BLACKBERRY_EMPLOYEE_DB_NAME
        blackberry_product_db_name = BLACKBERRY_PRODUCT_DB_NAME
        mongo_uri = MONGO_URI
        db_name = MONGO_DB_NAME
        stock_db_name = MONGO_STOCK_DB_NAME
        podcast_db_name = PODCAST_DB_NAME
        sales_insight_db_name = SALES_INSIGHT_DB_NAME
        product_insight_db_name = PRODUCT_INSIGHT_DB_NAME
        employee_insight_db_name = EMPLOYEE_INSIGHT_DB_NAME
        filings_db_name = FILINGS_DB_NAME
        feed_db_name = FEED_DB_NAME
        email_db_name = MONGO_EMAIL_DB_NAME
        max_pool_size = MONGO_MAX_POOL_SIZE
        min_pool_size = MONGO_MIN_POOL_SIZE
        max_idle_time_ms = MONGO_MAX_IDLE_TIME_MS

        retries = 0
        last_error = None

        while retries < max_retries:
            try:
                if self.client:
                    self.client.close()

                if self.blackberry_client:
                    self.blackberry_client.close()

                self.client = MongoClient(
                    mongo_uri,
                    maxPoolSize=max_pool_size,
                    minPoolSize=min_pool_size,
                    maxIdleTimeMS=max_idle_time_ms,
                    serverSelectionTimeoutMS=15000,
                    connectTimeoutMS=20000,
                    socketTimeoutMS=120000,
                    retryWrites=True,                # Enable retry for writes
                    retryReads=True,                 # Enable retry for reads
                    w="majority",
                    heartbeatFrequencyMS=10000
                )

                self.blackberry_client = MongoClient(
                    mongo_blackberry_uri,
                    maxPoolSize=max_pool_size,
                    minPoolSize=min_pool_size,
                    maxIdleTimeMS=max_idle_time_ms,
                    serverSelectionTimeoutMS=15000,
                    connectTimeoutMS=20000,
                    socketTimeoutMS=120000,
                    retryWrites=True,                # Enable retry for writes
                    retryReads=True,                 # Enable retry for reads
                    w="majority",
                    heartbeatFrequencyMS=10000
                )

                # Test connection
                self.client.admin.command('ping')
                self.blackberry_client.admin.command('ping')

                # Get database references
                self.blackberry_sales_db = self.blackberry_client[blackberry_sales_db_name]
                self.blackberry_employee_db = self.blackberry_client[blackberry_employee_db_name]
                self.blackberry_product_db = self.blackberry_client[blackberry_product_db_name]
                self.db = self.client[db_name]
                self.stock_db = self.client[stock_db_name]
                self.podcast_db = self.client[podcast_db_name]
                self.email_db = self.client[email_db_name]
                self.sales_insight_db = self.client[sales_insight_db_name]
                self.product_insight_db = self.client[product_insight_db_name]
                self.employee_insight_db = self.client[employee_insight_db_name]
                self.filings_db = self.client[filings_db_name]
                self.feed_db = self.client[feed_db_name]

                self.healthy = True
                logger.info(f"Successfully connected to MongoDB: {db_name} (Pool size: {max_pool_size})")
                return

            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                last_error = e
                retries += 1
                logger.warning(f"Failed to connect to MongoDB (attempt {retries}/{max_retries}): {e}")
                if retries < max_retries:
                    time.sleep(retry_delay)

        self.healthy = False
        logger.error(f"Failed to connect to MongoDB after {max_retries} attempts: {last_error}")
        raise ConnectionFailure(f"Could not connect to MongoDB after {max_retries} attempts") from last_error

    def ensure_connected(self):
        """Ensure that the connection to MongoDB is active"""
        if not self.healthy or not self.client or not self.blackberry_client:
            logger.info("Connection unhealthy, attempting to reconnect...")
            self._connect()
            return

        try:
            # Quick connection test
            self.client.admin.command('ping')
            self.blackberry_client.admin.command('ping')
        except Exception as e:
            logger.warning(f"Connection test failed: {e}. Attempting to reconnect...")
            self._connect()
    
    def get_blackberry_client(self):
        self.ensure_connected()
        return self.blackberry_client
    
    def get_blackberry_sales_collection(self, collection_name):
        self.ensure_connected()
        return self.blackberry_sales_db[collection_name]
    
    def get_blackberry_employee_collection(self, collection_name):
        self.ensure_connected()
        return self.blackberry_employee_db[collection_name]
    
    def get_blackberry_product_collection(self, collection_name):
        self.ensure_connected()
        return self.blackberry_product_db[collection_name]

    def get_db(self):
        self.ensure_connected()
        return self.db

    def get_client(self):
        self.ensure_connected()
        return self.client

    def get_collection(self, collection_name):
        self.ensure_connected()
        return self.db[collection_name]

    def get_stock_collection(self, collection_name):
        self.ensure_connected()
        return self.stock_db[collection_name]

    def get_podcast_collection(self, collection_name):
        self.ensure_connected()
        return self.podcast_db[collection_name]

    def get_email_collection(self, collection_name):
        self.ensure_connected()
        return self.email_db[collection_name]

    def get_sales_collection(self, collection_name):
        self.ensure_connected()
        return self.sales_insight_db[collection_name]

    def get_product_insight_collection(self, collection_name):
        self.ensure_connected()
        return self.product_insight_db[collection_name]

    def get_employee_insight_collection(self, collection_name):
        self.ensure_connected()
        return self.employee_insight_db[collection_name]

    def get_filings_collection(self, collection_name):
        self.ensure_connected()
        return self.filings_db[collection_name]

    def get_feed_collection(self, collection_name):
        self.ensure_connected()
        return self.feed_db[collection_name]

    def execute_with_retry(self, operation, max_retries=3, retry_delay=1):
        """Execute a MongoDB operation with automatic retries"""
        retries = 0
        last_error = None

        while retries < max_retries:
            try:
                self.ensure_connected()
                return operation()
            except (ConnectionFailure, ServerSelectionTimeoutError, AutoReconnect) as e:
                last_error = e
                retries += 1
                logger.warning(f"MongoDB operation failed (attempt {retries}/{max_retries}): {e}")
                if retries < max_retries:
                    time.sleep(retry_delay)

        logger.error(f"MongoDB operation failed after {max_retries} attempts: {last_error}")
        raise last_error

    def close_connection(self):
        if self.client:
            self.client.close()
            logger.info("MongoDB connection pool closed")
            self.client = None
            self.db = None
            self.stock_db = None
            self.email_db = None
            self.healthy = False
        
        if self.blackberry_client:
            self.blackberry_client.close()
            logger.info("MongoDB blackberry connection pool closed")
            self.blackberry_client = None
            self.blackberry_sales_db = None
            self.blackberry_employee_db = None
            self.blackberry_product_db = None
            self.healthy = False

    def get_connection_status(self):
        """Return status information about the connection pool"""
        if not self.client:
            return {"status": "not connected"}

        try:
            server_info = self.client.server_info()
            return {
                "status": "connected",
                "mongodb_version": server_info.get("version", "unknown"),
                "connection_pool": "active",
                "healthy": self.healthy
            }
        except Exception as e:
            self.healthy = False
            return {"status": "error", "message": str(e), "healthy": False}
