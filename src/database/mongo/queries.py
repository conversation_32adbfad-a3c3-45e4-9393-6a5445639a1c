from typing import Dict
from src.core.logging import get_logger
from pymongo.collection import Collection
from src.database.factory import DatabaseFactory

logger = get_logger(__name__)


def delete_records_marked_for_deletion(event_id: str, coll: Collection):
    coll.delete_many({"event_id": event_id, "mark_for_deletion": True})


def mark_events_for_deletion(event_id: str) -> Dict[str, int]:
    """
    Marks all records associated with a specific event_id for deletion across multiple collections.
    
    Args:
        event_id (str): The event ID to search for and mark records for deletion.
    
    Returns:
        Dict[str, int]: A dictionary mapping collection names to the number of modified documents.
    """
    if not event_id:
        logger.error("Invalid event_id provided")
        raise ValueError("event_id cannot be None or empty")

    collections = [
        "LLM_answers", "LLM_insights", "LLM_similarity", "LLM_trend", "embeddings", "qnas",
        "outputs", "entailments", "mda_outputs", "earnings_analysis", "earnings_anomalies",
        "mda_topics", "public_investor_events_outputs",
        "qna_flags", "qna_importance", "similar_questions", "mda_chunk_categories",
        "LLM_MDA_insights", "LLM_MDA_similarity", "LLM_MDA_trend"
    ]

    modification_results = {}
    total_modified = 0

    try:
        connection = DatabaseFactory().get_mongo_connection()

        for collection_name in collections:
            try:
                collection = connection.get_collection(collection_name)
                result = collection.update_many(
                    {"event_id": event_id},
                    {"$set": {"mark_for_deletion": True}}
                )
                modified_count = result.modified_count
                modification_results[collection_name] = modified_count
                total_modified += modified_count
                logger.info(f"Marked {modified_count} documents for deletion in {collection_name}")
            except Exception as e:
                logger.error(f"Error marking documents for deletion in {collection_name}: {str(e)}")
                modification_results[collection_name] = -1

        logger.info(f"Total documents marked for deletion across all collections: {total_modified}")

    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise

    return modification_results
