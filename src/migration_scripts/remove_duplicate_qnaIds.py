from src.database.factory import DatabaseFactory


def run():
    connection = DatabaseFactory().get_mongo_connection()
    qna_importance_collection = connection.get_collection("qna_importance")
    pipeline = [
        {
            "$group": {
                "_id": {"qnaId": "$qnaId", "event_id": "$event_id"},
                "count": {"$sum": 1},
            }
        },
        {"$match": {"count": {"$gt": 1}}},
        {"$project": {"_id": 0, "qnaId": "$_id.qnaId", "event_id": "$_id.event_id"}},
    ]
    ctr = 0

    with qna_importance_collection.aggregate(pipeline, allowDiskUse=True) as qnas:
        for qna in qnas:
            qnaId = qna["qnaId"]
            event_id = qna["event_id"]
            duplicate_qnas = list(qna_importance_collection.find({"qnaId": qnaId, "event_id": event_id}))
            if len(duplicate_qnas) > 1:
                documents_to_delete = duplicate_qnas[1:]
                if documents_to_delete:
                    ids_to_delete = [doc["_id"] for doc in documents_to_delete]
                    delete_result = qna_importance_collection.delete_many({
                        "_id": {"$in": ids_to_delete}
                    })
                    print(f"Deleted {delete_result.deleted_count} duplicate documents for qnaId: {qnaId}")

            ctr += 1
            print(f"Processed {ctr} document")


if __name__ == "__main__":
    run()
