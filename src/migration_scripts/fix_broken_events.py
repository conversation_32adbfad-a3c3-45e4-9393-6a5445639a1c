from src.database.factory import DatabaseFactory


def fix_broken_events():
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    public_investor_events_collection = connection.get_collection("public_investor_events")

    with public_investor_events_outputs_collection.find({"summary_body": {"$exists": False}}, no_cursor_timeout=True).sort("date", 1) as events:
        for i, event in enumerate(events):
            print(f"Started processing: {i}")
            _id = event["_id"]
            event_id = event["event_id"]
            events_collection_entry = public_investor_events_collection.find_one({"_id": event_id})
            if events_collection_entry:
                public_investor_events_collection.update_one(
                    {"_id": events_collection_entry["_id"]},
                    {"$set": {"select_for_qna_processing": 1}}
                )
            public_investor_events_outputs_collection.delete_one({"_id": _id})
            print(f"Completed processing: {i}")


if __name__ == "__main__":
    fix_broken_events()
