from src.database.factory import DatabaseFactory


def calcululate_mda_scores():
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")

    with public_investor_events_outputs_collection.find({}, no_cursor_timeout=True) as events_cursor:
        for event in events_cursor:
            event_id = event["event_id"]
            mda_upticks = llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "uptick"}) if llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "uptick"}) else 0
            mda_downticks = llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "downtick"}) if llm_mda_trend_collection.count_documents({"event_id": event_id, "trend": "downtick"}) else 0

            upticks = event["upticks"] or 0
            downticks = event["upticks"] or 0

            total_upticks = (upticks or 0) + (mda_upticks or 0)
            total_downticks = (downticks or 0) + (mda_downticks or 0)
            print(f"MDA Upticks: {mda_upticks}, MDA Downticks: {mda_downticks}")

            public_investor_events_outputs_collection.update_one(
                {"event_id": event_id},
                {"$set": {
                    "mda_upticks": mda_upticks,
                    "mda_downticks": mda_downticks,
                    "total_upticks": total_upticks,
                    "total_downticks": total_downticks
                }}
            )


if __name__ == "__main__":
    calcululate_mda_scores()
