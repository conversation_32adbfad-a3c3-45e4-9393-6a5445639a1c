from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory


def assign_importance_to_category(cutoff_date, lookback_days):
    print("Executing assign_importance_to_category")
    connection = DatabaseFactory().get_mongo_connection()
    categories_collection = connection.get_collection("sector_categories")
    qna_importance = connection.get_collection("qna_importance")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    # Load all sector_categories once and cache them
    print("Loading sector categories...")
    sector_categories_data = {}
    for category_doc in categories_collection.find():
        key = (category_doc["sector"], category_doc["category"])
        sector_categories_data[key] = category_doc
    print(f"Loaded {len(sector_categories_data)} sector categories")

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "is_importance_assigned": False
    }

    with qna_importance.find(query, no_cursor_timeout=True).batch_size(200) as qna_cursor:
        for qna in qna_cursor:
            qnaId = qna["qnaId"]
            print(f"Assigning importance to {qnaId}")
            sector = qna["sector"]
            category = qna["category"]
            if sector == "consumer":
                sector = "consumer_disc"

            # Use cached sector_categories instead of querying database
            sector_category = sector_categories_data.get((sector, category))
            if sector_category:
                category_importance = sector_category["importance"]
            else:
                print(f"Missing 'Category' or 'Importance' columns in sector_topics for sector: {sector}")
                category_importance = "Not Important"

            # Store the category back into MongoDB with upsert
            qna_importance.update_one(
                {"qnaId": qnaId},
                {"$set": {
                    "importance": category_importance,
                    "sector": sector,
                    "is_importance_assigned": True,
                    "updated_at": datetime.now()
                }},
                upsert=True
            )
            print(f"Category: {category}, Category Importance: {category_importance}")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    assign_importance_to_category(cutoff_date=cutoff_date, lookback_days=1000)
