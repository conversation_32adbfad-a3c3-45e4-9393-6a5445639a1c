import requests
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


def create_earnings_anomalies():
    # for every earnings event in public_investor_event_outputs, get the transcript from s3 and load into transcript, get closing stock price from stock_price_dataset the day after and the day before
    # cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    earnings_anomalies_collection = connection.get_collection("earnings_anomalies")

    stock_price_collection = connection.get_stock_collection("stock_price")

    # Check if there are already documents in earnings_anomalies for the given ticker and date range
    query = {
        "date": {"$gte": datetime(2025, 5, 31)}
    }
    earning_anomalies = list(earnings_anomalies_collection.find(query, no_cursor_timeout=True))

    for i, anomaly in enumerate(earning_anomalies):
        anomaly_old = anomaly["anomalies"]
        event_id = anomaly["event_id"]
        ticker = anomaly["ticker"]
        event = public_investor_events_outputs_collection.find_one({"event_id": event_id})
        date = event["date"]
        event_id = event["event_id"]
        date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        date_before = date - timedelta(days=1)
        date_after = date + timedelta(days=1)
        stock_price_before = stock_price_collection.find_one({"ticker": ticker, "date": date_before})
        tries = 0
        while not stock_price_before and tries < 10:
            date_before -= timedelta(days=1)
            stock_price_before = stock_price_collection.find_one({"ticker": ticker, "date": date_before})
            tries += 1

        stock_price_after = stock_price_collection.find_one({"ticker": ticker, "date": date_after})
        tries = 0
        while not stock_price_after and tries < 10:
            date_after += timedelta(days=1)
            stock_price_after = stock_price_collection.find_one({"ticker": ticker, "date": date_after})
            tries += 1

        if not stock_price_after:
            stock_price_after = {"closingPrice": 0}

        if not stock_price_before:
            stock_price_before = {"closingPrice": 0}

        if not stock_price_before or not stock_price_after:
            earnings_reaction = 0
        else:
            if stock_price_before["closingPrice"] == 0 or stock_price_after["closingPrice"] == 0:
                earnings_reaction = 0
            else:

                earnings_reaction = stock_price_after["closingPrice"] / stock_price_before["closingPrice"] - 1
                print(earnings_reaction)

        s3_url = event["s3_object_url"]
        # if s3_url is none continue
        if not s3_url:
            continue
        response = requests.get(s3_url)
        if response.status_code == 200:
            transcript = response.text
        else:
            print(f"Failed to fetch transcript from {s3_url}")
            continue

        earning_anomalies_summarization_prompt: str = prompt_collection.find_one({"prompt_name": "earning_anomalies_summarization_prompt", "version": 2})["prompt"]
        prompt = earning_anomalies_summarization_prompt.format(ticker=ticker, date=date, transcript=transcript)

        anomalies = openai_service.get_completion_without_limits(
            prompt=prompt,
            temperature=0,
            response_format={"type": "json_object"})
        anomalies = parse_json_from_markdown(anomalies)
        reported_quarter_text = ""
        for i, text in enumerate(anomalies["reported_quarter"]):
            reported_quarter_text += f'{i+1}. {text["factor"]} - "{text["reference_text"]}"\n'
        projected_quarter_text = ""
        for i, text in enumerate(anomalies["projected_quarter"]):
            projected_quarter_text += f'{i+1}. {text["factor"]} - "{text["reference_text"]}"\n'

        anomalies_text = f"Reported Quarter:\n{reported_quarter_text}\nProjected Quarter:\n{projected_quarter_text}"
        anomalies_text = anomalies_text.strip()

        earnings_anomalies_collection.update_one(
            {
                "_id": anomaly["_id"]
            },
            {
                "$set": {
                    "date": date,
                    "title": event["title"],
                    "ticker": ticker,
                    "anomalies": anomalies_text,
                    "anomalies_raw": anomalies,
                    "earnings_reaction": earnings_reaction,
                    "updated_at": datetime.now(),
                    "anomalies_old": anomaly_old
                }
            },
        )
        print(f"Completed: {i}")


if __name__ == "__main__":
    create_earnings_anomalies()
