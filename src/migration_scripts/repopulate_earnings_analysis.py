import json
import re
from datetime import datetime, timedelta, timezone

import pymongo

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService

logger = get_logger(__name__)


def create_earnings_calculation(
    earnings_analysis_numbers,
    revenue_quarterly_growth_percent="",
    revenue_yearly_growth_percent="",
    quarterly_operating_margin_bps_improvement="",
    yearly_operating_margin_bps_improvement="",
    previous_quarter_gross_margin="",
    next_quarter_operating_margin_improvement_bps=""
):
    calculation_parts = []

    # Add revenue with currency format
    currency = earnings_analysis_numbers.get("currency", "")
    revenue = earnings_analysis_numbers.get("reported_quarter_revenue")
    if revenue and isinstance(revenue, (int, float)) and currency:
        calculation_parts.append(f"Quarter Revenue: {currency} {revenue}")

    # Add other important metrics if they exist
    for key, label in [
        ("reported_quarter_gross_margin", "Gross Margin"),
        ("next_quarter_gross_margin_guidance", "Gross Margin Guidance"),
        ("reported_quarter_operating_margin", "Operating Margin"),
        ("next_quarter_revenue_growth_guidance", "Revenue Growth Guidance"),
        ("next_quarter_operating_margin_guidance", "Operating Margin Guidance"),
    ]:
        value = earnings_analysis_numbers.get(key)
        if value and isinstance(value, (int, float)):
            calculation_parts.append(f"{label}: {value}%")

    # Add growth and improvement metrics
    growth_metrics = [
        (revenue_quarterly_growth_percent, "QoQ Revenue Growth", "%"),
        (revenue_yearly_growth_percent, "YoY Revenue Growth", "%"),
        (quarterly_operating_margin_bps_improvement, "QoQ Margin Change", " bps"),
        (yearly_operating_margin_bps_improvement, "YoY Margin Change", " bps"),
        (previous_quarter_gross_margin, "Previous Gross Margin", "%"),
        (next_quarter_operating_margin_improvement_bps, "Expected Margin Improvement", " bps"),
    ]
    next_quarter_revenue_growth_guidance = earnings_analysis_numbers.get("next_quarter_revenue_growth_guidance")
    if revenue_quarterly_growth_percent and next_quarter_revenue_growth_guidance and isinstance(revenue_quarterly_growth_percent, (float, int)) and isinstance(next_quarter_revenue_growth_guidance, (float, int)):
        guidance_acc_or_dec = abs(abs(revenue_quarterly_growth_percent) - abs(next_quarter_revenue_growth_guidance)) * 100
        guidance_acc_or_dec = round(guidance_acc_or_dec, 1)
        if revenue_quarterly_growth_percent > next_quarter_revenue_growth_guidance:
            growth_metrics.append((guidance_acc_or_dec, "QoQ Revenue Guidance % Deceleration", "%"))
        else:
            growth_metrics.append((guidance_acc_or_dec, "QoQ Revenue Guidance % Acceleration", "%"))

    if revenue_yearly_growth_percent and next_quarter_revenue_growth_guidance and isinstance(revenue_yearly_growth_percent, (float, int)) and isinstance(next_quarter_revenue_growth_guidance, (float, int)):
        guidance_acc_or_dec = abs(abs(revenue_yearly_growth_percent) - abs(next_quarter_revenue_growth_guidance))
        guidance_acc_or_dec = round(guidance_acc_or_dec, 1)
        if revenue_yearly_growth_percent > next_quarter_revenue_growth_guidance:
            growth_metrics.append((guidance_acc_or_dec, "YoY Revenue Guidance % Deceleration", "%"))
        else:
            growth_metrics.append((guidance_acc_or_dec, "YoY Revenue Guidance % Acceleration", "%"))

    for value, label, unit in growth_metrics:
        if value and isinstance(value, (int, float)):
            calculation_parts.append(f"{label}: {value}{unit}")

    # Join all parts with semicolons
    calculation = "\n".join(calculation_parts)

    return calculation


def get_earnings_analysis_extraction_prompt(mda, current_quarter):
    earnings_analysis_extraction_prompt = """You are an expert financial analyst AI assistant. Your primary function is to meticulously analyze Management Discussion and Analysis (MD&A) sections from corporate earnings call transcripts.

Your task is to extract specific financial metrics and forward-looking guidance from the provided MD&A text. You must return this information STRICTLY in the JSON format specified below.

**Key Information to Extract:**

1.  **reported_quarter_revenue**: The total revenue reported for the most recently completed quarter discussed in the MD&A. Extract the numerical value ONLY as a precise number (e.g., 1200000000 for $1.2B, 500000000 for €500M).
2.  **currency**: The currency code in which the revenue is reported (e.g., "USD", "INR", "EUR", "RMB").
3.  **reported_quarter_gross_margin**: The gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 45.2 instead of "45.2%").
4.  **reported_quarter_operating_margin**: The operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 15.7 instead of "15.7%").
5.  **next_quarter_revenue_growth_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) revenue growth for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol. If provided as a specific amount, convert to the exact number.
6.  **next_quarter_operating_margin_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) operating margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 16.5 instead of "16.5%").
7.  **next_quarter_gross_margin_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) gross margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 12.3 instead of "12.3%").

**Output Format (JSON):**

Please structure your output precisely as follows. If a specific piece of information is not explicitly mentioned or cannot be reliably determined from the provided text, use `null` as the value for that key. Do not add any explanations or conversational text outside of the JSON object.
{{
  "reported_quarter_revenue": number | null,
  "currency": "string | null",
  "reported_quarter_gross_margin": number | null,
  "reported_quarter_operating_margin": number | null,
  "next_quarter_revenue_growth_guidance": number | null,
  "next_quarter_operating_margin_guidance": number | null,
  "next_quarter_gross_margin_guidance": number | null
}}

**Instructions for Extraction:**

- Focus SOLELY on the information present in the MD&A text provided. Do not infer information from external knowledge.
- For revenue, extract the numerical value and convert it to a precise number (e.g., convert $1.2B to 1200000000, €500M to 500000000).
- Extract the currency code (USD, EUR, INR, RMB, etc.) separately and include it in the "currency" field.
- For all percentage values, remove the % symbol and provide only the numerical value (e.g., 45.2 instead of "45.2%").
- For revenue and margins, prioritize figures explicitly stated for the "reported quarter" or "current quarter" being discussed.
- For guidance, look for forward-looking statements about the "next quarter," "upcoming quarter," or specific future period.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.
- Always give out a single number if available even by inference. For example, "low to mid-single digits" can be assumed as 4% and "45.5 to 46.5" can be assumed as 46%.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.

**Current Quarter**
{current_quarter}

**MD&A Text to Analyze:**

--- BEGIN MD&A TEXT ---
{mda}
--- END MD&A TEXT ---

Now, please process the MD&A text provided above and return the JSON output
"""
    earnings_analysis_extraction_prompt = earnings_analysis_extraction_prompt.format(
        mda=mda, current_quarter=current_quarter
    )
    return earnings_analysis_extraction_prompt


def get_generate_earnings_string_prompt(quarter, year, ticker, mda, mda_old, calculations, previous_quarter, last_year_same_quarter, next_quarter):
    prompt = """Look at the {quarter}-{year} earnings discussion for {ticker}, and compare it to prior management discussions, and fill out the operating metrics in the format provided. You can think as much as you like, but the response should ONLY be the portion in quotes. If any item cant be filled, leave it out of the analysys. for Other metrics, choose the one that is available. if you cannot find older data, try to calculate it. If current quarter revenue growth is more than prior quarter revenue growth, imply an acceleration, else a deceleration.

Calculations:
The number below are calculated for your reference. Prioritize the numbers below in your output if they are available
<beginning of calculations>
{calculations}
<end of calculations>

Use the following text for your analysis:
<beginning of text>
{mda}
<end of text>

For context in calculating changes, the prior quarter management discussion is as follows:
<beginning of text>
{mda_old}
<end of text>


Fill in the blanks for analysis:
"Revenue: In {quarter}-{year}, revenue increased _% year-over-year (YoY), compared to _% growth in {last_year_same_quarter}. Quarter-over-quarter (QoQ), revenue grew _%, versus _% in {previous_quarter}.
CRPO/Billings: Current Remaining Performance Obligations (CRPO) and Billings in {quarter}-{year} rose _% QoQ, compared to _% in {previous_quarter}.
Gross Margins: Gross margins for {quarter}-{year} were _%, representing a _ basis points (bps) change from {last_year_same_quarter}, and a _ bps change from {previous_quarter}.
Operating Margins: Operating margins stood at _% in {quarter}-{year}, a _ bps change compared to {last_year_same_quarter}, and a _ bps change versus {previous_quarter}.
Guidance: For {next_quarter}, revenue is expected to grow _% YoY, implying a _% deceleration from {quarter}-{year}’s YoY growth. This compares to a _% deceleration guided when {previous_quarter} results were reported. Operating margins for {next_quarter} are guided at _%, a _ bps change from {quarter}-{year}."

CRITICAL INSTRUCTIONS FOR HANDLING MISSING DATA:
When filling the template below:
1. If you cannot find a specific value in a sentence, REMOVE THE ENTIRE SENTENCE
2. If all sentences in a section must be removed, REMOVE THAT SECTION HEADING TOO
3. DO NOT include any blanks, underscores, placeholders, or "N/A" text in your output
4. ONLY include sentences where ALL values can be filled with actual numbers/percentages
5. Maintain the exact format of sentences that you can completely fill
6. Change the words improvement, slow down, accelaration, decelaration in the template based on the context of the change.
7. DO NOT make any assumptions or inferences about missing data
"""

    prompt = prompt.format(
        quarter=quarter,
        year=year,
        ticker=ticker,
        mda=mda,
        mda_old=mda_old,
        calculations=calculations,
        previous_quarter=previous_quarter,
        last_year_same_quarter=last_year_same_quarter,
        next_quarter=next_quarter,
    )
    return prompt


def repopulate_earnings_analysis():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    quant_data_collection = connection.get_stock_collection("quant_data")
    # prompt_collection = connection.get_collection("prompts")
    # companies_collection = connection.get_collection("companies")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    earnings_analysis_collection = connection.get_collection("earnings_analysis")

    end_date = datetime.strptime("2025-05-23", "%Y-%m-%d").replace(tzinfo=timezone.utc)

    # Set start date as 90 days prior to end date
    begin_date = (end_date - timedelta(days=90)).replace(tzinfo=timezone.utc)

    print(f"Creating earnings analysis for events between {begin_date} and {end_date}.")

    earnings_events = public_investor_events_outputs_collection.find({
        'event_type': 'earnings',
        'date': {'$gte': begin_date, '$lte': end_date}
    }, no_cursor_timeout=True).sort('date', 1)

    # Loop over each earnings event
    for i, event in enumerate(earnings_events):
        event_id = event["event_id"]
        event_date = event["date"]
        ticker = event["ticker"]

        print(f"Creating earnings analysis for event with ID {event_id} and {ticker}.")
        # check earnings analysis collection if event_id exists, if yes continue
        if event is None:
            print(f"Event with ID {event_id} not found.")
            continue

        if event["event_type"] != "earnings":
            print(f"Event with ID {event_id} is not an earnings event.")
            continue
        # find sector for ticker from companies

        # event_date = event["date"]
        event_title = event["title"]
        # start_date = event_date - timedelta(days=1)
        # end_date = event_date + timedelta(days=1)

        # from event title, extract the quarter and year
        quarter = None
        year = None
        if "Q1" in event_title:
            quarter = "Q1"
        elif "Q2" in event_title:
            quarter = "Q2"
        elif "Q3" in event_title:
            quarter = "Q3"
        elif "Q4" in event_title:
            quarter = "Q4"
        if "2024" in event_title:
            year = 2024
        elif "2023" in event_title:
            year = 2023
        elif "2025" in event_title:
            year = 2025

        if quarter is None or year is None:
            print(f"Could not extract quarter and year from event title: {event_title}")
            continue

        def get_previous_quarter(quarter, year):
            if quarter == "Q1":
                return "Q4 " + str(int(year) - 1)
            elif quarter == "Q2":
                return "Q1 " + str(year)
            elif quarter == "Q3":
                return "Q2 " + str(year)
            elif quarter == "Q4":
                return "Q3 " + str(year)
            return None

        def get_last_year_same_quarter(quarter, year):
            if quarter == "Q1":
                return "Q1 " + str(int(year) - 1)
            elif quarter == "Q2":
                return "Q2 " + str(int(year) - 1)
            elif quarter == "Q3":
                return "Q3 " + str(int(year) - 1)
            elif quarter == "Q4":
                return "Q4 " + str(int(year) - 1)
            return None

        def get_next_quarter(quarter, year):
            if quarter == "Q1":
                return "Q2 " + str(year)
            elif quarter == "Q2":
                return "Q3 " + str(year)
            elif quarter == "Q3":
                return "Q4 " + str(year)
            elif quarter == "Q4":
                return "Q1 " + str(int(year) + 1)
            return None

        previous_quarter = get_previous_quarter(quarter, year)
        last_year_same_quarter = get_last_year_same_quarter(quarter, year)
        next_quarter = get_next_quarter(quarter, year)

        mda = []
        qnas = qna_collection.find(
            {"event_id": event_id, "section": "MDA"}, no_cursor_timeout=True
        )
        for qna in qnas:
            if qna["section"] == "MDA":
                mda.append(qna["answer"])
        mda = " ".join(mda) if mda else None

        earnings_analysis_prompt = get_earnings_analysis_extraction_prompt(
            mda=mda, current_quarter=f"{quarter} {year}"
        )
        earnings_analysis_response = openai_service.get_completion_without_limits(
            prompt=earnings_analysis_prompt,
            temperature=0,
            response_format={"type": "json_object"},
        )
        earnings_analysis_numbers = json.loads(earnings_analysis_response)
        earnings_analysis_numbers["quarter"] = quarter
        earnings_analysis_numbers["year"] = year

        if not earnings_analysis_numbers["reported_quarter_revenue"]:
            quant_collection = quant_data_collection.find_one({
                "ticker": ticker,
                "fiscalperiod": f"{year}-{quarter}"
            })
            if quant_collection:
                earnings_analysis_numbers["reported_quarter_revenue"] = (
                    quant_collection["revenueusd"]
                )
                earnings_analysis_numbers["currency"] = "USD"

        reported_quarter_revenue = earnings_analysis_numbers["reported_quarter_revenue"]
        reported_quarter_operating_margin = earnings_analysis_numbers["reported_quarter_operating_margin"]
        next_quarter_operating_margin_guidance = earnings_analysis_numbers["next_quarter_operating_margin_guidance"]

        # find the last earnings event for the ticker
        last_quarter_earnings_event = earnings_analysis_collection.find_one(
            {
                "ticker": ticker,
                "quarter": previous_quarter.split()[0],
                "year": int(previous_quarter.split()[1]),
            }
        )
        if not last_quarter_earnings_event or not last_quarter_earnings_event.get("reported_quarter_revenue"):
            quant_collection = quant_data_collection.find_one({
                "ticker": ticker,
                "fiscalperiod": f"{previous_quarter.split()[1]}-{previous_quarter.split()[0]}"
            })
            last_quarter_earnings_event = {
                "reported_quarter_revenue": None,
                "currency": None,
                "reported_quarter_gross_margin": None,
                "reported_quarter_operating_margin": None,
                "next_quarter_revenue_growth_guidance": None,
                "next_quarter_operating_margin_guidance": None,
                "next_quarter_gross_margin_guidance": None
            }
            if quant_collection:
                last_quarter_earnings_event["reported_quarter_revenue"] = quant_collection["revenueusd"]
                last_quarter_earnings_event["currency"] = "USD"

        last_year_earnings_event = earnings_analysis_collection.find_one(
            {
                "ticker": ticker,
                "quarter": last_year_same_quarter.split()[0],
                "year": int(last_year_same_quarter.split()[1]),
            }
        )
        if not last_year_earnings_event or not last_year_earnings_event.get("reported_quarter_revenue"):
            quant_collection = quant_data_collection.find_one({
                "ticker": ticker,
                "fiscalperiod": f"{last_year_same_quarter.split()[1]}-{last_year_same_quarter.split()[0]}"
            })
            last_year_earnings_event = {
                "reported_quarter_revenue": None,
                "currency": None,
                "reported_quarter_gross_margin": None,
                "reported_quarter_operating_margin": None,
                "next_quarter_revenue_growth_guidance": None,
                "next_quarter_operating_margin_guidance": None,
                "next_quarter_gross_margin_guidance": None
            }
            if quant_collection:
                last_year_earnings_event["reported_quarter_revenue"] = quant_collection["revenueusd"]
                last_year_earnings_event["currency"] = "USD"

        revenue_quarterly_growth_percent = ""
        revenue_yearly_growth_percent = ""
        quarterly_operating_margin_bps_improvement = ""
        yearly_operating_margin_bps_improvement = ""
        previous_quarter_gross_margin = ""
        next_quarter_operating_margin_improvement_bps = ""

        if last_quarter_earnings_event:
            if reported_quarter_revenue and isinstance(reported_quarter_revenue, (int, float)):
                previous_quarter_revenue = last_quarter_earnings_event["reported_quarter_revenue"]
                if previous_quarter_revenue and isinstance(previous_quarter_revenue, (int, float)):
                    revenue_quarterly_growth_percent = round(((reported_quarter_revenue / previous_quarter_revenue) - 1) * 100, 1)

            if reported_quarter_operating_margin and isinstance(reported_quarter_operating_margin, (int, float)):
                previous_quarter_operating_margin = last_quarter_earnings_event["reported_quarter_operating_margin"]
                if previous_quarter_operating_margin and isinstance(previous_quarter_operating_margin, (int, float)):
                    quarterly_operating_margin_bps_improvement = int((previous_quarter_operating_margin - reported_quarter_operating_margin) * 100)

            if (
                next_quarter_operating_margin_guidance
                and isinstance(next_quarter_operating_margin_guidance, (int, float))
                and reported_quarter_operating_margin
                and isinstance(reported_quarter_operating_margin, (int, float))
            ):
                next_quarter_operating_margin_improvement_bps = int((next_quarter_operating_margin_guidance - reported_quarter_operating_margin) * 100)

        if last_year_earnings_event:
            if reported_quarter_revenue and isinstance(reported_quarter_revenue, (int, float)):
                previous_year_revenue = last_year_earnings_event["reported_quarter_revenue"]
                if previous_year_revenue and isinstance(previous_year_revenue, (int, float)):
                    revenue_yearly_growth_percent = round(((reported_quarter_revenue / previous_year_revenue) - 1) * 100, 1)

            if reported_quarter_operating_margin and isinstance(reported_quarter_operating_margin, (int, float)):
                previous_year_operating_margin = last_year_earnings_event["reported_quarter_operating_margin"]
                if previous_year_operating_margin and isinstance(previous_year_operating_margin, (int, float)):
                    yearly_operating_margin_bps_improvement = int((previous_year_operating_margin - reported_quarter_operating_margin) * 100)

        last_earnings_event = public_investor_events_outputs_collection.find_one({
            "ticker": ticker,
            "event_type": "earnings",
            "date": {"$lt": event_date}
        }, sort=[("date", pymongo.DESCENDING)])

        mda_old = []
        if last_earnings_event:
            last_event_id = last_earnings_event["event_id"]
            qnas = qna_collection.find({'event_id': last_event_id, 'section': 'MDA'}, no_cursor_timeout=True)
            for qna in qnas:
                if qna['section'] == "MDA":
                    mda_old.append(qna['answer'])
            mda_old = " ".join(mda_old) if mda_old else None
        else:
            print(f"No previous earnings event found for ticker {ticker}.")
            mda_old = None

        calculations = create_earnings_calculation(
            earnings_analysis_numbers,
            revenue_quarterly_growth_percent,
            revenue_yearly_growth_percent,
            quarterly_operating_margin_bps_improvement,
            yearly_operating_margin_bps_improvement,
            previous_quarter_gross_margin,
            next_quarter_operating_margin_improvement_bps
        )

        earnings_string_prompt = get_generate_earnings_string_prompt(quarter, year, ticker, mda, mda_old, calculations, previous_quarter, last_year_same_quarter, next_quarter)

        analysis = openai_service.get_completion_without_limits(earnings_string_prompt, temperature=0)

        analysis = re.sub(r'\s+', ' ', analysis).strip()
        analysis = analysis.strip('"')

        # Store the analysis in the database
        document = {
            "event_id": event["event_id"],
            "quarter": quarter,
            "ticker": ticker,
            "year": year,
            "analysis": analysis,
            "quarterly_revenue_growth_percent": revenue_quarterly_growth_percent,
            "yearly_revenue_growth_percent": revenue_yearly_growth_percent,
            "date": event["date"],
            "created_at": datetime.now(),
        }
        document.update(earnings_analysis_numbers)

        existing_earnings_analysis = earnings_analysis_collection.find_one({'event_id': event_id})

        if existing_earnings_analysis:
            print(f"Earnings analysis already exists for event with ID {event_id} and {ticker}.")
            earnings_analysis_collection.update_one(
                {'event_id': event['event_id'], 'quarter': quarter, 'year': year},
                {'$set': document},
            )
            print("Updated Event")
        # else:
        #     earnings_analysis_collection.insert_one(document=document)
        # print(existing_earnings_analysis)
        print(f"Completed: {i}")

    return


if __name__ == "__main__":
    repopulate_earnings_analysis()
