import os
from pymongo import MongoClient
import time
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from pymongo.errors import BulkWriteError, ConnectionFailure, ServerSelectionTimeoutError
import json
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()


def merge_collections_by_id(
    source_client,
    target_client,
    source_db: str,
    target_db: str,
    collection_name: str,
    batch_size: int = 1000,
    check_batch_size: int = 10000,
    max_retries: int = 3,
    retry_delay: int = 5,
    resume_from_id: Optional[Any] = None,
    progress_file: Optional[str] = None
) -> Tuple[int, int]:
    """
    Merge collections based on _id field with robust error handling and memory efficiency.
    Only inserts documents that don't exist in target collection.

    Args:
        source_client: MongoDB client for source database
        target_client: MongoDB client for target database
        source_db: Source database name
        target_db: Target database name
        collection_name: Collection name to merge
        batch_size: Size of insert batches
        check_batch_size: Size of batches when checking existing IDs
        max_retries: Maximum number of retries for failed operations
        retry_delay: Delay between retries in seconds
        resume_from_id: Resume processing from this _id (for recovery)
        progress_file: File to save progress for resumability

    Returns:
        Tuple of (total_processed, total_inserted)
    """

    # Input validation
    if not all([source_client, target_client, source_db, target_db, collection_name]):
        raise ValueError("All required parameters must be provided")

    if batch_size <= 0 or check_batch_size <= 0:
        raise ValueError("Batch sizes must be positive integers")

    try:
        source_collection = source_client[source_db][collection_name]
        target_collection = target_client[target_db][collection_name]

        # Ensure indexes exist for performance
        _ensure_indexes(target_collection)

        # Load progress if resuming
        progress = _load_progress(progress_file) if progress_file else {}
        start_from_id = resume_from_id or progress.get('last_processed_id')

        total_processed = progress.get('total_processed', 0)
        total_inserted = progress.get('total_inserted', 0)

        print(f"Starting merge for collection '{collection_name}'")
        if start_from_id:
            print(f"Resuming from ID: {start_from_id}")

        # Get total count for progress tracking
        query = {'_id': {'$gt': start_from_id}} if start_from_id else {}
        total_count = _safe_count(source_collection, query, max_retries, retry_delay)
        print(f"Total documents to process: {total_count}")

        # Process in memory-efficient batches
        batch = []
        last_id = start_from_id

        # Create cursor with proper configuration
        cursor_query = {'_id': {'$gt': start_from_id}} if start_from_id else {}
        cursor = source_collection.find(cursor_query).sort('_id', 1)

        # Configure cursor for large datasets
        cursor.batch_size(1000)  # MongoDB driver batch size

        try:
            for document in cursor:
                current_id = document['_id']

                # Check if document exists in target (batch check for efficiency)
                if not _document_exists_batch(target_collection, [current_id], max_retries, retry_delay):
                    batch.append(document)

                # Insert batch when full
                if len(batch) >= batch_size:
                    inserted_count = _safe_insert_batch(
                        target_collection, batch, max_retries, retry_delay
                    )
                    total_inserted += inserted_count
                    print(f"Inserted batch of {inserted_count}/{len(batch)} documents")
                    batch = []

                total_processed += 1
                last_id = current_id

                # Progress logging and saving
                if total_processed % 10000 == 0:
                    progress_pct = (total_processed / total_count * 100) if total_count > 0 else 0
                    print(f"Processed {total_processed}/{total_count} documents ({progress_pct:.1f}%)")

                    if progress_file:
                        _save_progress(progress_file, {
                            'last_processed_id': last_id,
                            'total_processed': total_processed,
                            'total_inserted': total_inserted,
                            'timestamp': datetime.now().isoformat()
                        })

        finally:
            cursor.close()

        # Insert remaining documents
        if batch:
            inserted_count = _safe_insert_batch(
                target_collection, batch, max_retries, retry_delay
            )
            total_inserted += inserted_count
            print(f"Inserted final batch of {inserted_count}/{len(batch)} documents")

        # Final progress save
        if progress_file:
            _save_progress(progress_file, {
                'last_processed_id': last_id,
                'total_processed': total_processed,
                'total_inserted': total_inserted,
                'completed': True,
                'timestamp': datetime.now().isoformat()
            })

        print(f"Merge completed successfully. Processed: {total_processed}, Inserted: {total_inserted}")
        return total_processed, total_inserted

    except Exception as e:
        print(f"Error during merge operation: {str(e)}")
        raise


def _ensure_indexes(collection):
    """Ensure _id index exists (should be automatic but verify)"""
    try:
        indexes = collection.list_indexes()
        has_id_index = any(idx.get('key', {}).get('_id') == 1 for idx in indexes)
        if not has_id_index:
            print("_id index not found, this may impact performance")
    except Exception as e:
        print(f"Could not check indexes: {e}")


def _safe_count(collection, query: Dict, max_retries: int, retry_delay: int) -> int:
    """Safely count documents with retry logic"""
    for attempt in range(max_retries):
        try:
            return collection.count_documents(query)
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            if attempt < max_retries - 1:
                print(f"Count attempt {attempt + 1} failed: {e}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                print(f"Failed to count documents after {max_retries} attempts")
                raise
    return 0


def _document_exists_batch(collection, ids: list, max_retries: int, retry_delay: int) -> bool:
    """Check if any documents exist in target collection"""
    for attempt in range(max_retries):
        try:
            result = collection.find_one({'_id': {'$in': ids}}, {'_id': 1})
            return result is not None
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            if attempt < max_retries - 1:
                print(f"Existence check attempt {attempt + 1} failed: {e}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                print(f"Failed to check document existence after {max_retries} attempts")
                raise
    return False


def _safe_insert_batch(collection, documents: list, max_retries: int, retry_delay: int) -> int:
    """Safely insert batch with retry logic and duplicate handling"""
    if not documents:
        return 0

    for attempt in range(max_retries):
        try:
            result = collection.insert_many(documents, ordered=False)
            return len(result.inserted_ids)

        except BulkWriteError as e:
            # Handle duplicate key errors gracefully
            inserted_count = e.details.get('nInserted', 0)
            write_errors = e.details.get('writeErrors', [])

            # Count only non-duplicate errors as real errors
            real_errors = [err for err in write_errors if err.get('code') != 11000]

            if real_errors:
                print(f"Bulk write had {len(real_errors)} non-duplicate errors")
                if attempt < max_retries - 1:
                    print(f"Retrying batch insert (attempt {attempt + 1})...")
                    time.sleep(retry_delay)
                    continue
                else:
                    raise

            print(f"Inserted {inserted_count} documents (some duplicates skipped)")
            return inserted_count

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            if attempt < max_retries - 1:
                print(f"Insert attempt {attempt + 1} failed: {e}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
            else:
                print(f"Failed to insert batch after {max_retries} attempts")
                raise

    return 0


def _save_progress(progress_file: str, progress_data: Dict):
    """Save progress to file for resumability"""
    try:
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, default=str, indent=2)
    except Exception as e:
        print(f"Could not save progress: {e}")


def _load_progress(progress_file: str) -> Dict:
    """Load progress from file"""
    try:
        with open(progress_file, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}
    except Exception as e:
        print(f"Could not load progress: {e}")
        return {}


# Enhanced main merge function for multiple collections
def merge_databases(
    source_client,
    target_client,
    source_db: str,
    target_db: str,
    collections: Optional[list] = None,
    **kwargs
) -> Dict[str, Tuple[int, int]]:
    """
    Merge entire database or specific collections with robust error handling.

    Args:
        source_client: MongoDB client for source database
        target_client: MongoDB client for target database
        source_db: Source database name
        target_db: Target database name
        collections: List of collection names to merge (None for all)
        **kwargs: Additional arguments passed to merge_collections_by_id

    Returns:
        Dictionary mapping collection names to (processed, inserted) counts
    """
    results = {}

    # Get list of collections to merge
    if collections is None:
        collections = source_client[source_db].list_collection_names()

    print(f"Starting database merge for {len(collections)} collections")

    for collection_name in collections:
        try:
            print(f"Processing collection: {collection_name}")

            # Set progress file for this collection
            progress_file = kwargs.get('progress_file')
            if progress_file:
                kwargs['progress_file'] = f"{progress_file}_{collection_name}.json"

            processed, inserted = merge_collections_by_id(
                source_client, target_client, source_db, target_db,
                collection_name, **kwargs
            )

            results[collection_name] = (processed, inserted)
            print(f"Completed {collection_name}: {processed} processed, {inserted} inserted")

        except Exception as e:
            print(f"Failed to merge collection {collection_name}: {e}")
            results[collection_name] = (0, 0)
            # Continue with other collections

    print("Database merge completed")
    return results


def create_clients_from_env():
    """Create clients using environment variables"""

    # Source client from environment
    source_uri = os.getenv('SOURCE_MONGODB_URI')
    print(source_uri)
    source_client = MongoClient(source_uri)

    # Target client from environment
    target_uri = os.getenv('TARGET_MONGODB_URI')
    print(target_uri)
    target_client = MongoClient(target_uri)

    return source_client, target_client


def run():
    source_client, target_client = create_clients_from_env()
    source_db = "slated_prod9_aux_bkp"
    target_db = "slated_prod7"
    merge_databases(source_client=source_client, target_client=target_client, source_db=source_db, target_db=target_db)


if __name__ == "__main__":
    run()
