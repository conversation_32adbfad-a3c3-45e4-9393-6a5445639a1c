SALES_REVIEWS_CATEGORIZATION_PROMPT = """
You are a financial analyst. The section between the <reviews start> and <reviews end> tags contains reviews from sales employees about the products of {slug}.
Your task is to categorize the reviews into different categories.
Output should be in JSON format,with fields "review_id", "category" and "review_text" with review_text under 100 words. 
Ignore text that is not related to the categories. 
Ignore text that is not about the company {slug}
The categories are:
{categories}

The reviews are
<reviews  start>
{reviews_articles_str}
<reviews end>

Sample output: (json format)
'review_id': _id, 'category': 'hiring trends', 'review_text': 'The company is hiring for a new product manager.'
"""

EMPLOYEE_REVIEWS_CATEGORIZATION_PROMPT = """
You are a financial analyst. The section between the <reviews start> and <reviews end> tags contains reviews from sales employees about the products of {ticker}.
Your task is to categorize the reviews into different categories.
Output should be in JSON format,with fields "review_id", "category" and "review_text" with review_text under 100 words. 
Ignore text that is not related to the categories. 
Ignore text that is not about the company {ticker}
The categories are:
{categories} 

The reviews are
<reviews  start>
{reviews_articles_str}
< reviews end>

Sample output: (json format)
'review_id': 12345, 'category': 'hiring trends', 'review_text': 'The company is hiring for a new product manager.'
"""