from typing import List
from src.reviews.base_review_processor import BaseReviewProcessor
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from pymongo.collection import Collection
from datetime import datetime
from src.services.llm.openai_service import OpenAIService
from src.reviews.prompts import EMPLOYEE_REVIEWS_CATEGORIZATION_PROMPT
from bson import ObjectId
import re
from src.reviews.schemas import ReviewCategory

logger = get_logger(__name__)
openai_service = OpenAIService()
connection = DatabaseFactory().get_mongo_connection()

categories = {
    "hiring trends",
    "quota and commissions",
    "revenue growth",
    "products",
    "lay offs",
    "competition to the company's products",
    "major customer wins",
    "major customer losses",
    " general positive comments about company",
    "general negative comments about company",
    "upcoming business changes",
    "general business description"
}

class EmployeeReviewProcessor(BaseReviewProcessor):
    def __init__(self, cutoff_date: datetime, end_date: datetime):
        super().__init__(cutoff_date, end_date)
        self.CUTOFF_DATE = cutoff_date
        self.END_DATE = end_date

        self.ticker_collection = connection.get_blackberry_employee_collection("blind_companies")
        self.articles_collection = connection.get_blackberry_employee_collection("articles")
        self.comments_collection = connection.get_blackberry_employee_collection("comments")

        self.insights_collection = connection.get_employee_insight_collection("blind_insights")
        self.review_chunks_collection = connection.get_employee_insight_collection("review_chunks")
    
    def _process_one_ticker(self, ticker: str) -> None:
        print(f"Processing ticker: {ticker}")
        query = {
            "company_ticker": ticker,
            "date": {
                "$gt": self.CUTOFF_DATE, 
                "$lt": self.END_DATE
            },
            "$expr": {
                "$eq": ["$author_company", "$company_slug"]
            }
        }
        review_article_string1 = self._preprocess_reviews(query, self.comments_collection, field_name="content", id_prefix="a_")
        review_article_string2 = self._preprocess_reviews(query, self.articles_collection, field_name="description", id_prefix="b_")
        reviews_articles_string = review_article_string1 + " " + review_article_string2
        review_list = self._categorize_reviews(EMPLOYEE_REVIEWS_CATEGORIZATION_PROMPT, ticker=ticker, categories=categories, reviews_articles_str=reviews_articles_string)

        for review in review_list:
            self._store_review_chunks(ticker, "", review)
        categories_for_ticker = self.review_chunks_collection.distinct("category", {"ticker": ticker})
        for category in categories_for_ticker:
            review_count = self.review_chunks_collection.count_documents({"ticker": ticker, "category": category})
            reviews = self.review_chunks_collection.find({"ticker": ticker, "category": category, "cutoff_date": self.CUTOFF_DATE})
            print(f"Number of reviews for category '{category}': {review_count}")
            review_text = " ".join([f"[review_id: {review['review_id_modified']}] {review['review_text']}" for review in reviews])
            if not review_text:
                print(f"No review text found for category: {category}")
                continue
            self._update_insights_collection(ticker, category, review_count, review_text)
        
    def _update_insights_collection(self, ticker: str, category: str, review_count: int, review_text: str):
        prompt = f"""
        You are a financial analyst summarizing insights from employee comments about {category}. 
        Summarize the following text into a 50-word insight. Use exact quotes if possible within quotation marks.
        For each quote also include the corresponding review_id in brackets.
        Output example:
        "The company is hiring for a new product manager. <sup>[review_id: 12345]</sup>"

        Text: {review_text}
        """
        summary = openai_service.get_completion(prompt)
        logger.info(f"Insight for category '{category}': {summary}")

        # extract all the ["review_id: "] from summary into a list
        review_ids = re.findall(r'\[review_id: ([\w, ]+)\]', summary)
        review_ids = [review_id.strip() for group in review_ids for review_id in group.split(',')]

        # Insert the summary into the sales_insights collection
        self.insights_collection.insert_one({
            "ticker": ticker,
            "cutoff_date": self.CUTOFF_DATE,
            "end_date": self.END_DATE,
            "category": category,
            "category_count": review_count,
            "summary": summary,
            "review_ids_modified": review_ids,
            "updated_at": datetime.now()
        })

    def _store_review_chunks(self, ticker: str, slug: str, review_data: ReviewCategory) -> bool:
        print(f"Review ID: {review_data.review_id}")
        print(f"Category: {review_data.category}")

        match review_data.review_id[0]:
            case "a":
                comment_id = review_data.review_id[2:]
                try:
                    comment_obj_id = ObjectId(comment_id)
                    review_id_obj = comment_obj_id
                except Exception as e:   
                    logger.info(f"Invalid ObjectId: {comment_id}. Error: {e}")
                    return False
                comment = self.comments_collection.find_one({"_id": comment_obj_id})
                source = "comment"

                if not comment:
                    print(f"Review not found for review_id: {comment_id}")
                    created_at = None
                    return False
                else:
                    created_at = comment.get("date")
            
            case "b":
                article_id = review_data.review_id[2:]
                try:
                    article_obj_id = ObjectId(article_id)
                    review_id_obj = article_obj_id
                except Exception as e:   
                    logger.info(f"Invalid ObjectId: {article_id}. Error: {e}")
                    return False
                article = self.articles_collection.find_one({"_id": article_obj_id})
                source = "article"

                if not article:
                    print(f"Review not found for review_id: {article_id}")
                    created_at = None
                    return False
                else:
                    created_at = article.get("date")

            case _:
                logger.info(f"Invalid review_id: {review_data.review_id}")
                created_at = None
                return False
        print(f"Review text: {review_data.review_text}")
        print(f"Created at: {created_at}")
        print(f"Source: {source}")
        print("-------------------------------------")
        self.review_chunks_collection.update_one(
            {"review_id_modified": review_data.review_id},
            {
                "$set": {
                    "ticker": ticker,
                    "cutoff_date": self.CUTOFF_DATE,
                    "end_date": self.END_DATE,
                    "review_id": review_id_obj,
                    "created_at": created_at,
                    "category": review_data.category,
                    "review_text": review_data.review_text,
                    "source": source,
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )

        return True

    def run(self):
        all_tickers = self.ticker_collection.distinct("ticker")
        for ticker in all_tickers:
            if self.insights_collection.find_one({"ticker": ticker, "cutoff_date": self.CUTOFF_DATE}):
                logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
                continue
            self._process_one_ticker(ticker)