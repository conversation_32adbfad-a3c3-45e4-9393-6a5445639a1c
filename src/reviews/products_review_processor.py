from src.reviews.base_review_processor import BaseReviewProcessor
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from pymongo.collection import Collection
from datetime import datetime
from src.services.llm.openai_service import OpenAIService
from src.reviews.prompts import PRODUCT_REVIEWS_CATEGORIZATION_PROMPT
from bson import ObjectId
from src.reviews.schemas import ReviewCategory
import re, os
from dotenv import load_dotenv
load_dotenv()

logger = get_logger(__name__)

openai_service = OpenAIService()
connection = DatabaseFactory().get_mongo_connection()

class ProductReviewProcessor(BaseReviewProcessor):
    def __init__(self, cutoff_date: datetime, end_date: datetime):
        super().__init__(cutoff_date, end_date)
        self.CUTOFF_DATE = cutoff_date
        self.END_DATE = end_date
        