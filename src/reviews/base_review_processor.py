import re
import json
from src.core.logging import get_logger, configure_logging
from src.database.factory import DatabaseFactory
from src.database.mongo.connection import MongoDBConnection
from src.services.llm.openai_service import OpenAIService
from pymongo.collection import Collection
from typing import List, TypedDict, Any
from datetime import datetime, timedelta
from pydantic import TypeAdapter, ValidationError
from src.reviews.schemas import ReviewCategory

logger = get_logger(__name__)
configure_logging()

openai_service = OpenAIService()

class BaseReviewProcessor:
    def __init__(self, cutoff_date: datetime, end_date: datetime):
        self.CUTOFF_DATE: datetime = cutoff_date
        self.END_DATE: datetime = end_date

        self.prompts_collection: Collection
        self.ticker_collection: Collection
        self.reviews_collection: Collection
        self.insights_collection: Collection
        self.review_chunks_collection: Collection
    
    def _preprocess_reviews(self, query: dict, collection: Collection,  field_name: str = "content", id_prefix: str = "") -> str:
        """
        Formats raw reviews into standardized text strings with review IDs
        """
        if  collection.count_documents(query) == 0:
            logger.info(f"No comments found for ticker. Skipping...")
            review_articles = []
        else:
            review_articles = collection.find(query)

        reviews_articles_str = ""
        for review_article in review_articles:
            try:
                reviews_articles_str += f"review_id: {id_prefix}{review_article['_id']} "
                reviews_articles_str += f"review_{field_name}: {review_article[field_name]} "
            except KeyError as e:
                print(f"KeyError in {review_article}: {e}")
                continue

        return reviews_articles_str
    
    def _categorize_reviews(self, prompt_template: str, **kwargs) -> List[ReviewCategory]:
        """
        Extracts and validates JSON from AI response with error handling
        """
        prompt = prompt_template.format_map(kwargs)
        response = openai_service.get_completion(prompt=prompt, temperature=0)
        review_list = self._parse_categorization_results(response)
        return review_list

    def _parse_categorization_results(self, ai_response: str) -> List[ReviewCategory]:
        """
        Extracts and validates JSON from AI response with error handling
        """
        match = re.search(r'(\{\s*\"review_id.*\}|\[.*\])', ai_response, re.DOTALL)      
        if not match:
            print(f"No valid JSON found in OpenAI response - Skipping.")
            return []
        json_str = match.group(1).strip()
        try:
            review_list = json.loads(match.group(1)) 
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON categories : {e}")
            return []

        if isinstance(review_list, dict):
            review_list = [review_list] 

        adapter = TypeAdapter(List[ReviewCategory])
        try:
            validated_reviews = adapter.validate_python(review_list)
        except ValidationError as e:
            print(f"Validation error: {e}")
            return []
        except Exception as e:
            print(f"Unknown error in JSON validation: {e}")
            return []

        return validated_reviews

    def _store_review_chunks(self, ticker: str, slug: str, review_data: ReviewCategory) -> bool:
        """
        Upserts individual categorized review chunks to database
        """
        return True

    def _generate_category_summary(self, review_text: str, category: str) -> str:
        """
        Creates AI-generated summaries for each category with quote attribution
        """
        return ""

    def _extract_review_ids(self, summary_text: str) -> list:
        """
        Parses review IDs from summary text using regex patterns
        """
        review_ids = re.findall(r'\[review_id: ([\w, ]+)\]', summary_text)
        review_ids = [review_id.strip() for group in review_ids for review_id in group.split(',')]
        return review_ids

    def run(self):
        pass
