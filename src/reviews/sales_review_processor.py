from src.reviews.base_review_processor import BaseReviewProcessor
from typing import List
from datetime import datetime
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger, configure_logging
from pymongo.collection import Collection
from src.services.llm.openai_service import OpenAIService
from src.reviews.prompts import SALES_REVIEWS_CATEGORIZATION_PROMPT
from bson import ObjectId
from src.reviews.schemas import ReviewCategory
import re, os
from dotenv import load_dotenv
load_dotenv()

logger = get_logger(__name__)
configure_logging()

openai_service = OpenAIService()

categories = {
    "hiring trends",
    "quota and commissions",
    "revenue growth",
    "products",
    "lay offs",
    "competition to the company's products",
    "major customer wins",
    "major customer losses",
    "general positive comments about company",
    "general negative comments about company",
    "upcoming business changes",
    "general business description"
}

connection = DatabaseFactory().get_mongo_connection()

class SalesReviewProcessor(BaseReviewProcessor):
    def __init__(self, cutoff_date: datetime, end_date: datetime):
        super().__init__(cutoff_date, end_date)
        self.CUTOFF_DATE = cutoff_date
        self.END_DATE = end_date

        self.ticker_collection = connection.get_blackberry_sales_collection("ticker_identifier")
        self.reviews_collection = connection.get_blackberry_sales_collection("reviews")
        self.insights_collection = connection.get_sales_collection("sales_insights")
        self.review_chunks_collection = connection.get_sales_collection("review_chunks")
    
    def _store_review_chunks(self, ticker: str, slug: str, review_data: ReviewCategory) -> bool:
        try:
            review_id = ObjectId(review_data.review_id)
        except Exception as e:   
            print(f"Invalid ObjectId: {review_data.review_id}. Error: {e}")
            return False
        
        sales_review = self.reviews_collection.find_one({"_id": review_id})
        if not sales_review:
            print(f"Review not found for review_id: {review_id}")
            return False
        created_at = sales_review.get("created_at")

        print(f"Review ID: {review_id}")
        print(f"Review text: {review_data.review_text}")
        print(f"Category: {review_data.category}")
        print(f"Created at: {created_at}")
        print(f"Slug: {slug}")
        print("-------------------------------------")
        self.review_chunks_collection.update_one(
            {"review_id": review_id},
            {
                "$set": {
                    "ticker": ticker,
                    "cutoff_date": self.CUTOFF_DATE,
                    "end_date": self.END_DATE,
                    "created_at": created_at,
                    "slug": slug,
                    "category": review_data.category,
                    "review_text": review_data.review_text,
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )
        return True

    def _generate_category_summary(self, review_text: str, category: str):
        prompt = f"""
        You are a financial analyst summarizing insights from sales comments about {category}. 
        Summarize the following text into a 50-word insight. Use exact quotes if possible within quotation marks.
        For each quote also include the corresponding review_id in brackets.
        Output example:
        "The company is hiring for a new product manager. <sup>[review_id: 12345]</sup>"

        Text: {review_text}
        """
        summary = openai_service.get_completion(prompt=prompt)
        return summary
    
    def _update_insights_collection(self, ticker: str, slug: str, category: str, review_count: int):
        reviews = self.review_chunks_collection.find({"ticker": ticker, "category": category, "cutoff_date": self.CUTOFF_DATE})        
        review_text = " ".join([f"[review_id: {review['review_id']}] {review['review_text']}" for review in reviews])   # Check if review_text is empty
        if not review_text:
            print(f"No review text found for category: {category}")
            return ""
        summary = self._generate_category_summary(review_text, category)
        review_ids = self._extract_review_ids(summary)
        print(f"Review IDs: {review_ids}")
        self.insights_collection.insert_one({
            "ticker": ticker,
            "cutoff_date": self.CUTOFF_DATE,
            "end_date": self.END_DATE,
            "slug": slug,
            "category": category,
            "category_count": review_count,
            "summary": summary,
            "review_ids": review_ids,
            "updated_at": datetime.now()
        })

    def _aggregate_insights(self, ticker: str, slug: str):
        "Groups reviews by category, counts occurrences, generates  final insights"
        total_category_count = self.insights_collection.count_documents({"ticker": ticker})
        if total_category_count == 0:
            logger.info(f"No categories found for ticker {ticker}. Skipping...")
            return
        
        #if total category_count is greater than 0, find the most common category
        most_common_category = self.insights_collection.aggregate([
            {
                "$match": {"ticker": ticker, "cutoff_date": self.CUTOFF_DATE}
            },
            {
                "$group": {
                    "_id": "$category",
                    "count": {"$sum": 1}
                }
            },
            {
                "$sort": {"count": -1}
            },
            {
                "$limit": 1
            }
        ])   # get the most common category
        most_common_category = list(most_common_category)
        if not most_common_category:
            print(f"No most common category found for ticker: {ticker}")
            return
        most_common_category_data = most_common_category[0]
        most_common_category = most_common_category_data["_id"]
        # get the most common category count
        most_common_category_count = most_common_category_data["count"]
        # find %ge of most common category
        most_common_category_percentage = (most_common_category_count / total_category_count) * 100

        # set all flags to false for ticker
        self.insights_collection.update_many(
            {"ticker": ticker},
            {"$set": {"flag": False}}
        )
        if most_common_category_percentage > 33:
            print(f"Most common category for {ticker} is {most_common_category} with percentage {most_common_category_percentage}")
            self.insights_collection.update_one(
                {"ticker": ticker, "category": most_common_category},
                {"$set": {"flag": True}}
            )

    def _process_one_ticker(self, ticker: str, slug: str) -> None:
        if self.insights_collection.find_one({"ticker": ticker, "cutoff_date": self.CUTOFF_DATE}):
            logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
            return
        print(f"Processing ticker {ticker}")
        reviews_collection_query = {
            "created_at": {"$gt": self.CUTOFF_DATE.strftime("%Y-%m-%d"), "$lt": self.END_DATE.strftime("%Y-%m-%d")},
            "slug": slug
        }
        reviews_articles_string = self._preprocess_reviews(reviews_collection_query, self.reviews_collection, field_name="text")
        review_list = self._categorize_reviews(SALES_REVIEWS_CATEGORIZATION_PROMPT, reviews_articles_str=reviews_articles_string, slug=slug, categories=categories)
        for review in review_list:
            self._store_review_chunks(ticker, slug, review)

        categories_for_ticker = self.review_chunks_collection.distinct("category", {"ticker": ticker})
        for category in categories_for_ticker:
            review_count = self.review_chunks_collection.count_documents({"ticker": ticker, "category": category})
            if review_count == 0:
                print(f"No reviews found for category {category}. Skipping...")
                continue
            print(f"Number of reviews for category '{category}': {review_count}")
            self._update_insights_collection(ticker, slug, category, review_count)
        
        self._aggregate_insights(ticker, slug)

    def run(self) -> None:
        for doc in self.ticker_collection.find():
            ticker = doc["ticker"]
            slug = doc["slug"]
            self._process_one_ticker(ticker, slug)