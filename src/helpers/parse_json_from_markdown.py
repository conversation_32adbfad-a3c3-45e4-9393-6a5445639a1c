import re
import json


def parse_json_from_markdown(input_str):
    """
    Extract and parse JSON from a Markdown code fence.
    Falls back to parsing the entire string if no fence is found.
    """
    fence_re = re.compile(r'```(?:json)?\s*([\s\S]*?)```', re.IGNORECASE)
    m = fence_re.search(input_str)
    json_text = m.group(1) if m else input_str

    json_text = json_text.strip()

    try:
        return json.loads(json_text)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e}")
