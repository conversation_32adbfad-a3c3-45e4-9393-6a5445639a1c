import threading
import signal
import sys
from src.core.constants import LOG_LEVEL
from src.core.logging import get_logger, configure_logging

configure_logging(log_level=LOG_LEVEL)
logger = get_logger(__name__)


class FeedGeneratorDaemon:
    def __init__(self):
        self.feed_generator = None
        self.shutdown_event = threading.Event()

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
        if self.feed_generator:
            self.feed_generator.stop_monitoring()
        sys.exit(0)

    def run(self):
        """Main daemon run method"""
        # Register signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        try:
            from src.feeds.feed import FeedGenerator
            self.feed_generator = FeedGenerator()

            # Perform initial sync
            logger.info("Starting initial sync...")
            self.feed_generator.initial_sync()

            # Start real-time monitoring
            logger.info("Starting real-time monitoring...")
            self.feed_generator.start_monitoring()

            # Keep the service running
            logger.info("Feed generator daemon is running...")
            while not self.shutdown_event.is_set():
                self.shutdown_event.wait(1)

        except Exception as e:
            logger.error(f"Fatal error in feed generator daemon: {e}")
            if self.feed_generator:
                self.feed_generator.stop_monitoring()
            sys.exit(1)


if __name__ == "__main__":
    daemon = FeedGeneratorDaemon()
    daemon.run()
