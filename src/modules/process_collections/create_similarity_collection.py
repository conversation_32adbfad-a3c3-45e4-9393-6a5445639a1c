import json
from pymongo import InsertOne
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.database.mongo.queries import delete_records_marked_for_deletion


logger = get_logger(__name__)


def get_generate_difference_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss, similar_mda_chunks):
    """
    Generate a prompt to ask OpenAI to classify a qna into Very Different/Somewhat Different/Similar, and a 100 word rationale
    """
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")

    similar_qna_faiss_text = " ".join(
        [
            f"Question: {item['question']} Answer: {item['answer']}"
            for item in similar_qna_faiss[:3]  # CHANGE: Only pick the top 3 questions from FAISS
        ]
    )

    # mda = "\n\nManagement Discussions:\n"
    # for _chunk in similar_mda_chunks:
    #     mda += _chunk["chunk"] + "\n"

    similar_qna_faiss_text = similar_qna_faiss_text  # + mda
    generate_difference_prompt = prompt_collection.find_one({"prompt_name": "generate_difference_prompt"})["prompt"]

    prompt = generate_difference_prompt.format(
        answer_speaker=answer_speaker,
        question_speaker=question_speaker,
        question=question,
        answer=answer,
        similar_qna_faiss_text=similar_qna_faiss_text,
    )
    return prompt


def create_similarity_collection(cutoff_date, lookback_days, min_question_length, min_answer_length, analysis_window_days):
    """
    Loop through each question in similar_questions and ask OpenAI to differentiate the answer from previous answers.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    similar_questions_collection = connection.get_collection("similar_questions")
    llm_similarity_collection = connection.get_collection("LLM_similarity")
    qna_importance_collection = connection.get_collection("qna_importance")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers},
    }

    with similar_questions_collection.find(query, no_cursor_timeout=True).batch_size(200) as similar_questions_cursor:

        batch = []

        for similar_question in similar_questions_cursor:
            qnaId = similar_question["qnaId"]
            event_id = similar_question["event_id"]
            date = similar_question["date"]
            question_speaker = similar_question["question_speaker"]
            question = similar_question["question"]
            answer_speaker = similar_question["answer_speaker"]
            answer = similar_question["answer"]
            ticker = similar_question["ticker"]
            delete_records_marked_for_deletion(event_id=event_id, coll=llm_similarity_collection)

            if date < cutoff_date_start or date > cutoff_date_end:
                continue

            # continue if qnaId already exists in LLM_similarity collection
            if llm_similarity_collection.find_one({"qnaId": qnaId}):
                logger.info("LLM similarity already exists")
                continue

            if len(question) < min_question_length:
                continue

            if len(answer) < min_answer_length:
                continue

            # Get Similar questions from the `similar_questions` collection for this qnaId
            similar_questions_faiss = similar_question.get("related_qna_faiss", [])
            # CHANGE: Add similar for MDA using the category

            qna_importance_entry = qna_importance_collection.find_one({"qnaId": qnaId})
            if not qna_importance_entry:
                continue
            qna_category = qna_importance_entry["category"]

            mda_chunk_analysis_end = date - timedelta(days=1)
            mda_chunk_analysis_start = mda_chunk_analysis_end - timedelta(days=analysis_window_days)
            mda_chunk_category_query = {
                "category": qna_category,
                "ticker": ticker,
                "date": {"$gte": mda_chunk_analysis_start, "$lte": mda_chunk_analysis_end}
            }

            similar_mda_chunks = mda_chunk_categories_collection.find(mda_chunk_category_query, no_cursor_timeout=True)
            logger.info("Fetched chunks from MDA similar to the question")

            prompt = get_generate_difference_prompt(question_speaker, question, answer_speaker, answer, similar_questions_faiss, similar_mda_chunks)
            # Ask OpenAI to differentiate the answer
            differentiation = openai_service.get_completion(prompt, response_format={"type": "json_object"})
            if not differentiation:
                logger.info("no data")
                continue
            if differentiation == []:
                logger.info("no data")
                continue

            if isinstance(differentiation, dict):
                data = differentiation
            else:
                data = json.loads(differentiation)

            classification = data["classification"]
            rationale = data["rationale"]
            logger.info(classification)
            logger.info(rationale)

            # Store the differentiation back in MongoDB
            document = {
                "qnaId": qnaId,
                "ticker": similar_question["ticker"],
                "question": question,
                "answer": answer,
                "classification": classification,
                "rationale": rationale,
                "date": similar_question["date"],
                "event_id": similar_question["event_id"],
                "event_name": similar_question["event_name"],
                "updated_at": datetime.now(),
            }
            batch.append(InsertOne(document))

            if len(batch) == 100:
                llm_similarity_collection.bulk_write(batch)
                batch = []

        if batch:
            llm_similarity_collection.bulk_write(batch)
            batch = []
        llm_similarity_collection.create_index("qnaId", unique=True)
        logger.info("LLM_similarity collection created")
        return


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_similarity_collection(cutoff_date, 1, 10, 50, 180)
