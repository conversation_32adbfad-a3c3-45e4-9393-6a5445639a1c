import json
from bson.objectid import ObjectId
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.services.bedrock import BedrockManager
from src.database.factory import DatabaseFactory
from src.services.text_splitter import TextSplitter
from src.services.llm.openai_service import OpenAIService
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def assign_category_to_mda_chunks(answer_chunks: list, ticker: str):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    companies_collection = connection.get_collection("companies")
    sector_categories_collection = connection.get_collection("sector_categories")
    company = companies_collection.find_one({"ticker": ticker})
    sector = company["sector"] if company else None
    sectors = sector_categories_collection.find({"sector": sector}, no_cursor_timeout=True)
    # categories = [document["category"] for document in sectors]
    category_importance_dict = {}
    for document in sectors:
        category_importance_dict[document["category"]] = document["importance"]
    categories = category_importance_dict.keys()

    categories_text = "\n-".join(categories)
    categorize_mda_chunks = prompt_collection.find_one({"prompt_name": "categorize_mda_chunks"})["prompt"]

    # TODO: Make requests in parallel when it works
    for answer_chunk in answer_chunks:
        categorize_mda_chunks_prompt = categorize_mda_chunks.format(
            classification_list_text=categories_text,
            answer=answer_chunk["chunk"],
            sector=sector,
        )
        response = openai_service.get_completion(
            prompt=categorize_mda_chunks_prompt, response_format={"type": "json_object"}
        )
        response = json.loads(response)
        extracted_category = response["category"]
        try:
            answer_chunk["category"] = extracted_category
            answer_chunk["importance"] = category_importance_dict[extracted_category]
        except Exception:
            answer_chunk["category"] = extracted_category
            answer_chunk["importance"] = "Somewhat Important"
            logger.error(f"Got wrong category for Importance for MDA Chunk: {answer_chunk['chunk']}, Got Category: {extracted_category}")
    return answer_chunks


def process_answer(
    answer: str,
    answer_speaker: str,
    qna_id: ObjectId,
    ticker: str,
    section: str,
    date: str,
    event_id: ObjectId,
    event_name: str
):
    splitter = TextSplitter()
    chunks = splitter.split(answer)
    answer_chunks = []
    for i in range(len(chunks)):
        answer_chunks.append(
            {
                "qnaId": qna_id,
                "chunk_id": f"{str(qna_id)}_{i}",
                "chunk": chunks[i],
                "date": date,
                "ticker": ticker,
                "answer_speaker": answer_speaker,
                "event": event_name,
                "event_id": event_id,
                "updated_at": datetime.now(),
                "select_for_llm_similarity_mda_processing": 1,
                "select_for_llm_answer_similarity_processing": 1,
                "select_for_mda_output_processing": 1
            }
        )
    if section != "MDA":
        return answer_chunks
    categorized_answer_chunks = assign_category_to_mda_chunks(answer_chunks, ticker)
    return categorized_answer_chunks


def create_embeddings_collection(cutoff_date, lookback_days):
    """Creates embeddings for each qna using bedrock titan 2"""

    connection = DatabaseFactory().get_mongo_connection()
    bedrock_manager = BedrockManager()
    qna_collection = connection.get_collection("qnas")
    embedding_collection = connection.get_collection("embeddings")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_embeddings_processing": 1,
    }

    ctr = 0
    batch = []
    mda_docs = []
    batch_size = 100

    with qna_collection.find(query, no_cursor_timeout=True).batch_size(50) as qna_cursor:
        for question in qna_cursor:
            event_id = question["event_id"]
            delete_records_marked_for_deletion(event_id=event_id, coll=embedding_collection)
            delete_records_marked_for_deletion(event_id=event_id, coll=mda_chunk_categories_collection)
            qna_collection.update_one(
                {"_id": question["_id"]},
                {"$set": {"select_for_embeddings_processing": 0}},
            )
            if question["date"] < cutoff_date_start or question["date"] > cutoff_date_end:
                logger.info("qna for embedding is outside dates")
                continue

            if embedding_collection.find_one({"qnaId": question["_id"]}):
                logger.info("embedding already exists")
                continue

            if mda_chunk_categories_collection.find_one({"qnaId": question["_id"]}):
                logger.info("Entry already exists in mda chunks embeddings collection")
                continue

            qnaId = question["_id"]
            ticker = question["ticker"]
            question_text = question["question"]
            answer_text = question["answer"]
            date = question["date"]
            section = question["section"]
            question_embedding = (
                bedrock_manager.get_embeddings(question["question"])
                if question["question"]
                else None
            )
            question_speaker = question["question_speaker"]
            answer_speaker = question["answer_speaker"]
            answer_chunks = process_answer(
                answer=answer_text,
                answer_speaker=answer_speaker,
                qna_id=qnaId, ticker=ticker,
                section=section, date=date,
                event_id=question["event_id"],
                event_name=question["event"]
            ) if answer_text else None

            if answer_chunks and section == "MDA":
                mda_docs.extend(answer_chunks)

            event_name = question["event"]
            event_type = "earnings" if "earnings" in event_name.lower() else "non_earnings"
            document = {
                "qnaId": qnaId,
                "ticker": ticker,
                "question": question_text,
                "answer": answer_text,
                "date": date,
                "question_embedding": question_embedding,
                "question_speaker": question_speaker,
                "answer_speaker": answer_speaker,
                "answer_chunks": answer_chunks,
                "section": section,
                "event_id": question["event_id"],
                "event_name": question["event"],
                "event_type": event_type,
                "updated_at": datetime.now(),
            }

            batch.append(document)
            ctr += 1

            if len(mda_docs) >= batch_size:
                mda_chunk_categories_collection.insert_many(mda_docs)
                logger.info(f"Inserted {len(mda_docs)} documents to MDA Docs collection")
                mda_docs = []

            if len(batch) >= batch_size:
                embedding_collection.insert_many(batch)
                batch = []
                logger.info(f"Processed {ctr} documents for embeddings collection")

    if batch:
        embedding_collection.insert_many(batch)
        logger.info(f"Final: Processed {ctr} documents for embeddings collection")

    if mda_docs:
        mda_chunk_categories_collection.insert_many(mda_docs)
        logger.info(f"Final: Inserted {len(mda_docs)} documents to MDA Docs collection")

    logger.info("Embedding collection creation completed.")


if __name__ == "__main__":
    create_embeddings_collection("2025-06-03", 20)
