import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from src.services.bedrock import BedrockManager
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def create_llm_answer_collection(cutoff_date, lookback_days, min_question_length, min_answer_length):
    openai_service = OpenAIService()
    bedrock_manager = BedrockManager()
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    companies_collection = connection.get_collection("companies")
    llm_answer_collection = connection.get_collection("LLM_answers")
    similar_qns_ans_collection = connection.get_collection("similar_questions")
    prompt_collection = connection.get_collection("prompts")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}, "ticker": {"$in": tickers}
    }

    with qna_collection.find(query, no_cursor_timeout=True).batch_size(200) as qna_cursor:
        MIN_LLM_ANSWER_LENGTH = 100
        MAX_CONTEXT_LENGTH = 6500  # 8192 max
        MAX_LLM_ANSWER_LENGTH = 500

        # Convert cursor to list and then to DataFrame
        questions_list = list(qna_cursor)
        questions_df = pd.DataFrame(questions_list)

        ctr = 0
        for index, question in questions_df.iterrows():
            qnaId = question["_id"]
            event_id = question["event_id"]
            current_ticker = question["ticker"]
            question_speaker = question["question_speaker"]
            answer_speaker = question["answer_speaker"]
            current_question = question["question"]
            current_answer = question["answer"]

            delete_records_marked_for_deletion(event_id=event_id, coll=llm_answer_collection)

            if current_answer is None:
                continue

            current_date = question["date"]
            current_answer_length = len(current_answer.split())
            max_answer_length = max(MIN_LLM_ANSWER_LENGTH, current_answer_length, MAX_LLM_ANSWER_LENGTH)

            if current_date > cutoff_date_end:
                continue
            if current_date < cutoff_date_start:
                continue

            # if qnaId exists in LLM_answers collection, continue
            if llm_answer_collection.find_one({"qnaId": qnaId}):
                logger.info("LLM answer already exists")
                continue

            if current_question is None:
                continue

            if current_answer is None:
                continue

            if answer_speaker is None:
                continue

            if question_speaker is None:
                continue

            if len(current_question) < min_question_length:
                continue

            if len(current_answer) < min_answer_length:
                continue

            related_qna_faiss_string = ""

            # Get the related_qna_faiss from the similar_questions collection if qnaId is the same as qnaId, and store it as a datafrae
            document = similar_qns_ans_collection.find_one({"qnaId": qnaId})

            # Check if the document and related_qna_faiss array exist
            if document and 'related_qna_faiss' in document:
                related_qna_faiss_array = document['related_qna_faiss']

                # Convert the related_qna_faiss array to a DataFrame
                related_qna_faiss_df = pd.DataFrame(related_qna_faiss_array)

                # dates = related_qna_faiss_df['date']
                # related_qna_faiss_string = " ".join(related_qna_faiss_df['answer'].dropna().astype(str).tolist())
                related_qna_faiss_string = " ".join(related_qna_faiss_df.apply(lambda row: f"Question: {row['question']}   CFO's Answer: {row['answer']}", axis=1).tolist())
                related_qna_faiss_string = related_qna_faiss_string.replace("\n", " ")

                mangement_qa_reponse_prompt = prompt_collection.find_one({"prompt_name": "mangement_qa_reponse_prompt"})["prompt"]

                # Ensure the dictionary contains the 'answer_speaker' key
                estimated_prompt_length = len(mangement_qa_reponse_prompt.format(
                    related_qna_faiss_string="",
                    current_question=current_question,
                    max_answer_length=max_answer_length,
                    answer_speaker=answer_speaker if 'answer_speaker' in locals() else 'Unknown'
                ).split())

                # Calculate the maximum allowed length for related_qna_faiss_string
                max_related_qna_faiss_length = MAX_CONTEXT_LENGTH - estimated_prompt_length

                related_qna_faiss_string_tokens = related_qna_faiss_string.split()

                if len(related_qna_faiss_string_tokens) > max_related_qna_faiss_length:
                    related_qna_faiss_string = " ".join(related_qna_faiss_string_tokens[:max_related_qna_faiss_length])

                prompt = mangement_qa_reponse_prompt.format(
                    related_qna_faiss_string=related_qna_faiss_string,
                    current_question=current_question,
                    max_answer_length=max_answer_length,
                    answer_speaker=answer_speaker
                )

                generation = openai_service.get_completion(prompt)

                generated_answer = generation

                if not current_answer:
                    current_answer = "No answer available"

                if not generated_answer:
                    generated_answer = "No answer available"

                # similarity with generater answer

                embedding1 = bedrock_manager.get_embeddings(current_answer)
                embedding1_np = np.array(embedding1)

                embedding2 = bedrock_manager.get_embeddings(generated_answer)
                embedding2_np = np.array(embedding2)

                dot_product = np.dot(embedding1_np, embedding2_np)
                norm1 = np.linalg.norm(embedding1_np)
                norm2 = np.linalg.norm(embedding2_np)
                similarity_generated = dot_product / (norm1 * norm2)

                document = {
                    "qnaId": qnaId,
                    "event_id": event_id,
                    "ticker": current_ticker,
                    "question": current_question,
                    "question_speaker": question_speaker,
                    "answer_speaker": answer_speaker,
                    "date": current_date,
                    "answer": current_answer,
                    "LLM_answer_llama": generated_answer,
                    "LLM_answer_llama_similarity": similarity_generated,
                    "updated_at": datetime.now()
                }
                llm_answer_collection.insert_one(document)
                ctr += 1
                logger.info(ctr)
