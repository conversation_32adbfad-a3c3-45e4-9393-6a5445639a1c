import time
from datetime import datetime, timedelta

from src.core.logging import get_logger
from src.services.s3 import S3Client
from src.database.factory import DatabaseFactory
from src.services.sp_global import SPGlobalClient

logger = get_logger(__name__)


DEFAULT_DELAY_SECONDS = 2
S3_URL_EXPIRATION_SECONDS = 36000000


def upload_sp_events_to_s3(cutoff_date: str, lookback_days: int, delay_seconds: int = DEFAULT_DELAY_SECONDS) -> int:
    """
    Download event files from SP Global, upload them to S3, and update MongoDB with S3 URLs.

    Args:
        cutoff_date (str): End date for the event search in format 'YYYY-MM-DD'.
        lookback_days (int): Number of days to look back from the cutoff date.
        delay_seconds (int, optional): Delay between API calls to prevent rate limiting.
                                      Defaults to 5 seconds.

    Returns:
        int: The number of events successfully uploaded to S3.
    """
    sp_client = SPGlobalClient()
    s3 = S3Client()

    connection = DatabaseFactory().get_mongo_connection()
    events_collection = connection.get_collection("public_investor_events")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    events = events_collection.find({"s3_object_url": {"$exists": False}}, no_cursor_timeout=True)

    upload_count = 0

    for event in events:
        unique_event_id = event["unique_event_id"]
        file_name = event["file_name"]
        file_date = event["date"]
        event_id = event["_id"]

        if not (cutoff_date_start < file_date < cutoff_date_end):
            continue

        time.sleep(delay_seconds)
        xml_content = sp_client.download_from_sp(unique_event_id)
        time.sleep(delay_seconds)

        if xml_content is None:
            logger.warning(f"Failed to download content for event ID: {unique_event_id}")
            continue

        object_name = f"{event_id}_{file_name}"

        logger.info(f'Uploading {object_name} to S3')
        s3.put_object(s3_key=object_name, data=xml_content, metadata={"Content-Type": "application/xml"})
        upload_count += 1

        presigned_url = s3.generate_presigned_url(
            s3_key=object_name,
            expiration=S3_URL_EXPIRATION_SECONDS
        )

        events_collection.update_one(
            {"_id": event_id},
            {"$set": {"s3_object_url": presigned_url}}
        )

        time.sleep(delay_seconds)

    logger.info(f"Uploaded {upload_count} events to S3")
    return upload_count


# if __name__ == "__main__":
#     upload_sp_events_to_s3("2025-04-18", 1780)
