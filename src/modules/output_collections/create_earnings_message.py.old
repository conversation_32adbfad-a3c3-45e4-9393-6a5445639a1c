import pymongo
from datetime import datetime, timedelta, timezone
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger

logger = get_logger(__name__)


def create_earnings_message(cutoff_date, lookback_days=3):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    prompt_collection = connection.get_collection("prompts")
    companies_collection = connection.get_collection("companies")
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    earnings_analysis_collection = connection.get_collection("earnings_analysis")

    if isinstance(cutoff_date, str):
        end_date = datetime.strptime(cutoff_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    else:
        end_date = cutoff_date if cutoff_date.tzinfo else cutoff_date.replace(tzinfo=timezone.utc)

    begin_date = end_date - timedelta(days=lookback_days)

    logger.info(f"Creating earnings analysis for events between {begin_date} and {end_date}.")

    # Get the earnings events for the given date range
    earnings_events = public_investor_events_outputs_collection.find({
        'event_type': 'earnings',
        'date': {'$gte': begin_date, '$lt': end_date}
    }, no_cursor_timeout=True)

    # Loop over each earnings event
    for event in earnings_events:
        event_id = event["event_id"]
        ticker = event["ticker"]

        logger.info(f"Creating earnings analysis for event with ID {event_id} and {ticker}.")
        # check earnings analysis collection if event_id exists, if yes continue
        if earnings_analysis_collection.find_one({'event_id': event_id}):
            logger.info(f"Earnings analysis already exists for event with ID {event_id} and {ticker}.")
            continue

        if event is None:
            logger.info(f"Event with ID {event_id} not found.")
            continue

        if event["event_type"] != "earnings":
            logger.info(f"Event with ID {event_id} is not an earnings event.")
            continue
        # find sector for ticker from companies
        company = companies_collection.find_one({"ticker": ticker})
        if company:
            sector = company["sector"]
        else:
            sector = "general"

        event_date = event["date"]
        event_title = event["title"]
        # start_date = event_date - timedelta(days=1)
        # end_date = event_date + timedelta(days=1)

        # from event title, extract the quarter and year
        quarter = None
        year = None
        if "Q1" in event_title:
            quarter = "Q1"
        elif "Q2" in event_title:
            quarter = "Q2"
        elif "Q3" in event_title:
            quarter = "Q3"
        elif "Q4" in event_title:
            quarter = "Q4"
        if "2024" in event_title:
            year = 2024
        elif "2023" in event_title:
            year = 2023
        elif "2025" in event_title:
            year = 2025

        if quarter is None or year is None:
            logger.info(f"Could not extract quarter and year from event title: {event_title}")
            continue

        def get_previous_quarter(quarter, year):
            if quarter == "Q1":
                return "Q4 " + str(int(year) - 1)
            elif quarter == "Q2":
                return "Q1 " + str(year)
            elif quarter == "Q3":
                return "Q2 " + str(year)
            elif quarter == "Q4":
                return "Q3 " + str(year)
            return None

        def get_last_year_same_quarter(quarter, year):
            if quarter == "Q1":
                return "Q1 " + str(int(year) - 1)
            elif quarter == "Q2":
                return "Q2 " + str(int(year) - 1)
            elif quarter == "Q3":
                return "Q3 " + str(int(year) - 1)
            elif quarter == "Q4":
                return "Q4 " + str(int(year) - 1)
            return None

        def get_next_quarter(quarter, year):
            if quarter == "Q1":
                return "Q2 " + str(year)
            elif quarter == "Q2":
                return "Q3 " + str(year)
            elif quarter == "Q3":
                return "Q4 " + str(year)
            elif quarter == "Q4":
                return "Q1 " + str(int(year) + 1)
            return None

        previous_quarter = get_previous_quarter(quarter, year)
        last_year_same_quarter = get_last_year_same_quarter(quarter, year)
        next_quarter = get_next_quarter(quarter, year)

        mda = []
        qnas = qna_collection.find({'event_id': event_id, 'section': 'MDA'}, no_cursor_timeout=True)
        for qna in qnas:
            if qna['section'] == "MDA":
                mda.append(qna['answer'])
        mda = " ".join(mda) if mda else None

        mda_old = []
        # find the last earnings event for the ticker
        last_earnings_event = public_investor_events_outputs_collection.find_one({
            "ticker": ticker,
            "event_type": "earnings",
            "date": {"$lt": event_date}
        }, sort=[("date", pymongo.DESCENDING)])

        if last_earnings_event:
            last_event_id = last_earnings_event["event_id"]
            qnas = qna_collection.find({'event_id': last_event_id, 'section': 'MDA'}, no_cursor_timeout=True)
            for qna in qnas:
                if qna['section'] == "MDA":
                    mda_old.append(qna['answer'])
            mda_old = " ".join(mda_old) if mda_old else None
        else:
            logger.info(f"No previous earnings event found for ticker {ticker}.")
            mda_old = None

        # extract prompt from prompts collection
        prompt_test = prompt_collection.find_one({"file": "create_earnings_note", "function": "create_earnings_message", "sector": sector})

        raw_prompt = prompt_test["prompt"]

        prompt = raw_prompt.format(
            quarter=quarter,
            year=year,
            ticker=ticker,
            previous_quarter=previous_quarter,
            last_year_same_quarter=last_year_same_quarter,
            next_quarter=next_quarter,
            mda=mda,
            mda_old=mda_old
        )

        logger.info(f"Prompt: {prompt}")

        response = openai_service.get_completion(prompt)

        logger.info(f"Analysis Response:, {response}")
        clean_earnings_analysis_prompt = prompt_collection.find_one({"prompt_name": "clean_earnings_analysis_prompt"})["prompt"]
        clean_earnings_analysis_prompt = clean_earnings_analysis_prompt.format(response=response)
        response2 = openai_service.get_completion(clean_earnings_analysis_prompt)

        # extract the content between the last two " " in the response

        response_split = response2.split('"')
        if len(response_split) >= 3:
            response3 = response_split[-2]
        else:
            response3 = response2
            logger.info("Error: Unable to extract content between quotes.")

        logger.info(f"Cleaned Analysis Response: {response3}")

        # Store the analysis in the database
        document = {
            'event_id': event['event_id'],
            'quarter': quarter,
            'ticker': ticker,
            'year': year,
            'analysis': response3,
            "date": event["date"],
            'created_at': datetime.now()
        }

        # Insert the document into the collection
        earnings_analysis_collection.update_one(
            {'event_id': event['_id'], 'quarter': quarter, 'year': year},
            {'$set': document},
            upsert=True
        )
    return
