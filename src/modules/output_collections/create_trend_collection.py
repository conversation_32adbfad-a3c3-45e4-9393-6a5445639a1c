import json
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_trend_prompt(question, answer, similar_questions_faiss, classification, rationale, max_words, similar_mda_chunks):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    trend_prompt = prompt_collection.find_one({"prompt_name": "trend_prompt"})["prompt"]
    similar_qna_faiss_text = " ".join(
        [
            f"Question: {item['question']} Answer: {item['answer']}"
            for item in similar_questions_faiss[:3]  # Only pick the top 3 questions from FAISS
        ]
    )
    # mda = "\n\nManagement Discussions:\n"
    # for _chunk in similar_mda_chunks:
    #     mda += _chunk["chunk"] + "\n"

    # similar_qna_faiss_text = similar_qna_faiss_text + mda
    prompt = trend_prompt.format(question=question, answer=answer, similar_qna_faiss=similar_qna_faiss_text, classification=classification, rationale=rationale, max_words=max_words)
    return prompt


def create_trend_collection(cutoff_date, lookback_days, max_words, analysis_window_days):
    """
    Loop through each question in LLM_similarity and ask OpenAI to classify the trend of the answer.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    LLM_similarity_collection = connection.get_collection("LLM_similarity")
    similar_question_collection = connection.get_collection("similar_questions")
    companies_collection = connection.get_collection("companies")
    llm_trend_collection = connection.get_collection("LLM_trend")
    qna_importance_collection = connection.get_collection("qna_importance")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers}
    }

    # Process in batches to avoid cursor timeout
    batch_size = 100
    skip = 0

    while True:
        # Get a batch of documents instead of keeping a cursor open for a long time
        batch = list(LLM_similarity_collection.find(query).skip(skip).limit(batch_size))
        if not batch:
            break

        for LLM_similarity in batch:
            qnaId = LLM_similarity["qnaId"]
            date = LLM_similarity["date"]
            event_id = LLM_similarity["event_id"]
            question = LLM_similarity["question"]
            answer = LLM_similarity["answer"]
            classification = LLM_similarity["classification"]
            rationale = LLM_similarity["rationale"]
            ticker = LLM_similarity["ticker"]
            delete_records_marked_for_deletion(event_id=event_id, coll=llm_trend_collection)

            if llm_trend_collection.find_one({"qnaId": qnaId}):
                logger.info("LLM trend already exists")
                continue

            if classification == "Similar":
                continue

            # Get related_qna_faiss from the `similar_questions` collection for this qnaId
            similar_question = similar_question_collection.find_one({"qnaId": qnaId})
            similar_questions_faiss = similar_question.get("related_qna_faiss", []) if similar_question else []

            if date < cutoff_date_start or date > cutoff_date_end:
                continue

            # Get similar MDA chunks using the category
            qna_importance_entry = qna_importance_collection.find_one({"qnaId": qnaId})
            if not qna_importance_entry:
                logger.info(f"No QnA importance entry found for qnaId: {qnaId}")
                continue

            qna_category = qna_importance_entry["category"]
            mda_chunk_analysis_end = date - timedelta(days=1)
            mda_chunk_analysis_start = mda_chunk_analysis_end - timedelta(days=analysis_window_days)
            mda_chunk_category_query = {
                "category": qna_category,
                "ticker": ticker,
                "event_id": {"$ne": event_id},
                "date": {"$gte": mda_chunk_analysis_start, "$lte": mda_chunk_analysis_end}
            }

            # Fetch MDA chunks into a list instead of keeping cursor open
            similar_mda_chunks = list(mda_chunk_categories_collection.find(mda_chunk_category_query).limit(10))

            # Create the prompt
            trend_prompt = get_trend_prompt(
                question=question,
                answer=answer,
                similar_questions_faiss=similar_questions_faiss,
                classification=classification,
                rationale=rationale,
                max_words=max_words,
                similar_mda_chunks=similar_mda_chunks
            )

            try:
                # Ask OpenAI to differentiate the answer
                trend = openai_service.get_completion(trend_prompt)
                if isinstance(trend, dict):
                    data = trend
                else:
                    data = json.loads(trend)

                trend_classification = data["trend"]
                logger.info(trend_classification)
                trend_rationale = data["rationale"]
                logger.info(trend_rationale)

                # Store the differentiation back in MongoDB
                document = {
                    "qnaId": qnaId,
                    "ticker": LLM_similarity["ticker"],
                    "event_id": event_id,
                    "question": question,
                    "answer": answer,
                    "classification": classification,
                    "rationale": rationale,
                    "trend": trend_classification,
                    "trend_rationale": trend_rationale,
                    "date": LLM_similarity["date"],
                    "updated_at": datetime.now()
                }
                llm_trend_collection.insert_one(document)

            except Exception as e:
                logger.error(f"Error processing qnaId {qnaId}: {str(e)}")
                continue

        # Move to the next batch
        skip += batch_size

    return
