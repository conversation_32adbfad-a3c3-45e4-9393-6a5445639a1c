from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_insight_refinement_prompt():
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    prompt = prompt_collection.find_one({"prompt_name": "insight_refinement_prompt_v2"})["prompt"]
    return prompt


def create_insights_collection(cutoff_date, lookback_days, batch_size=100):
    """
    Loop through each question in LLM_trend and ask OpenAI to generate insights
    using batch processing to prevent cursor timeouts
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    LLM_trend_collection = connection.get_collection("LLM_trend")
    llm_insights_collection = connection.get_collection("LLM_insights")
    companies_collection = connection.get_collection("companies")
    qna_importance_collection = connection.get_collection("qna_importance")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers},
        "event_id": {"$exists": True}
    }

    # Get the insight generation prompt once outside the loop
    insight_refinement_prompt = get_insight_refinement_prompt()

    # Count total documents for progress tracking
    total_documents = LLM_trend_collection.count_documents(query)
    processed_documents = 0

    while processed_documents < total_documents:
        # Process in batches to avoid cursor timeout
        batch_cursor = LLM_trend_collection.find(query).skip(processed_documents).limit(batch_size)
        batch_data = list(batch_cursor)

        if not batch_data:
            break

        logger.info(f"Processing batch of {len(batch_data)} documents. Progress: {processed_documents}/{total_documents}")

        for LLM_trend in batch_data:
            qnaId = LLM_trend["qnaId"]
            event_id = LLM_trend["event_id"]
            date = LLM_trend["date"]
            question = LLM_trend["question"]
            answer = LLM_trend["answer"]
            trend = LLM_trend["trend"]
            trend_rationale = LLM_trend["trend_rationale"]

            delete_records_marked_for_deletion(event_id=event_id, coll=llm_insights_collection)

            # Skip if insight already exists
            if llm_insights_collection.find_one({"qnaId": qnaId}):
                logger.info(f"LLM insight already exists for qnaId: {qnaId}")
                continue

            # Skip if outside date range (redundant check but kept for safety)
            if date < cutoff_date_start or date > cutoff_date_end:
                continue

            # Find category and importance from qna_importance for qnaId
            qna_importance_doc = qna_importance_collection.find_one({"qnaId": qnaId})
            if qna_importance_doc:
                category = qna_importance_doc.get("category", "Uncategorized")
                importance = qna_importance_doc.get("importance", "Not Important")
            else:
                category = "Uncategorized"
                importance = "Not Important"

            # Create the prompt
            prompt = insight_refinement_prompt.format(trend=trend, trend_rationale=trend_rationale, question=question, answer=answer)

            # Ask OpenAI to generate insight
            insight = openai_service.get_completion_without_limits(prompt, temperature=0)
            insight = insight.replace("'", "\"")
            logger.info(insight)

            # Store the insight in MongoDB
            document = {
                "qnaId": qnaId,
                "ticker": LLM_trend["ticker"],
                "event_id": event_id,
                "question": question,
                "answer": answer,
                "rationale": trend_rationale,
                "trend": trend,
                "insight": insight,
                "date": LLM_trend["date"],
                "category": category,
                "importance": importance,
                "updated_at": datetime.now()
            }
            llm_insights_collection.insert_one(document)

        # Update processed count
        processed_documents += len(batch_data)

    logger.info(f"Completed processing all {processed_documents} documents.")
    return


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    create_insights_collection(cutoff_date, 500)
