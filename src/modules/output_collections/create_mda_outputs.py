from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def create_mda_outputs(cutoff_date, lookback_days, analysis_window_days):
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    llm_mda_similarity_collection = connection.get_collection("LLM_MDA_similarity")
    llm_mda_insights_collection = connection.get_collection("LLM_MDA_insights")
    public_investor_events_collection = connection.get_collection("public_investor_events")
    mda_outputs_collection = connection.get_collection("mda_outputs")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)
    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_mda_output_processing": 1
    }

    with mda_chunk_categories_collection.find(query, no_cursor_timeout=True) as mda_chunk_categories_cursor:
        for mda_chunk_category in mda_chunk_categories_cursor:
            event_id = mda_chunk_category["event_id"]
            delete_records_marked_for_deletion(event_id=event_id, coll=mda_outputs_collection)
            mda_chunk_id = mda_chunk_category["_id"]
            if mda_outputs_collection.find_one({"mda_chunk_id": mda_chunk_id}):
                logger.info("MDA Output already exists")
                continue

            mda_chunk_analysis_end = mda_chunk_category["date"] - timedelta(days=1)
            mda_chunk_analysis_start = mda_chunk_analysis_end - timedelta(days=analysis_window_days)
            mda_chunk_category_query = {
                "category": mda_chunk_category["category"],
                "ticker": mda_chunk_category["ticker"],
                "event_id": {"$ne": mda_chunk_category["event_id"]},
                "date": {"$gte": mda_chunk_analysis_start, "$lte": mda_chunk_analysis_end}
            }
            similar_mda_chunks = mda_chunk_categories_collection.find(mda_chunk_category_query, no_cursor_timeout=True)
            similar_mda_chunk_ids = [chunk["_id"] for chunk in similar_mda_chunks]

            llm_mda_trend = llm_mda_trend_collection.find_one({"mda_chunk_id": mda_chunk_id})
            if not llm_mda_trend:
                logger.warning(f"No LLM_MDA_trend found for mda_chunk_id: {mda_chunk_id}")

            llm_mda_insights = llm_mda_insights_collection.find_one({"mda_chunk_id": mda_chunk_id})
            if not llm_mda_insights:
                logger.warning(f"No LLM_MDA_insights found for mda_chunk_id: {mda_chunk_id}")

            llm_mda_similarity = llm_mda_similarity_collection.find_one({"mda_chunk_id": mda_chunk_id})
            if not llm_mda_similarity:
                logger.warning(f"No LLM_MDA_similarity found for mda_chunk_id: {mda_chunk_id}")

            event_info = public_investor_events_collection.find_one({"_id": mda_chunk_category["event_id"]})
            if not event_info:
                logger.warning(f"No event_info found for event_id: {mda_chunk_category['event_id']}")
                continue

            company = companies_collection.find_one({"ticker": mda_chunk_category["ticker"]})

            document = {
                "mda_chunk_id": mda_chunk_id,
                "event_id": mda_chunk_category["event_id"],
                "chunk_id": llm_mda_similarity.get("chunk_id", None) if llm_mda_similarity else None,
                "qnaId": mda_chunk_category["qnaId"],
                "category": mda_chunk_category["category"],
                "LLM_insight": llm_mda_insights.get("insight", None) if llm_mda_insights else None,
                "LLM_rationale_final": llm_mda_similarity.get("rationale", None) if llm_mda_similarity else None,
                "LLM_trend_final": llm_mda_trend.get("trend", None) if llm_mda_trend else None,
                "LLM_trend_rationale_final": llm_mda_trend.get("trend_rationale", None) if llm_mda_trend else None,
                "MDA_text_current": mda_chunk_category["chunk"],
                "classification_final": llm_mda_similarity.get("classification") if llm_mda_similarity else None,
                "date": mda_chunk_category["date"],
                "reference_current": mda_chunk_category["chunk"],
                "reference_historical": similar_mda_chunk_ids,
                "sector": company["sector"],
                "ticker": mda_chunk_category["ticker"],
                "title": event_info.get("title", None) if event_info else None,
                "event_type": event_info.get("event_type") if event_info else None,
                "updated_at": datetime.now()
            }
            mda_outputs_collection.insert_one(document)
            mda_chunk_categories_collection.update_one(
                {"_id": mda_chunk_id},
                {"$set": {"select_for_mda_output_processing": 0}}
            )


if __name__ == "__main__":
    create_mda_outputs("2025-05-28", 1780, 180)
