from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger

logger = get_logger(__name__)


def get_generate_summary_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss, max_words_in_summary):
    """
    Generate a prompt to ask OpenAI to summarize the reason why the current answer differs from previous answers.
    """
    similar_qna_faiss_text = " ".join([f"Question: {item['question']} Answer: {item['answer']}" for item in similar_qna_faiss])
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    generate_summary_prompt = prompt_collection.find_one({"prompt_name": "generate_summary_prompt"})["prompt"]
    prompt = generate_summary_prompt.format(answer_speaker=answer_speaker, question_speaker=question_speaker, question=question, answer=answer, similar_qna_faiss_text=similar_qna_faiss_text, max_words_in_summary=max_words_in_summary)
    return prompt


def add_LLM_rationale_to_entailment(cutoff_date, lookback_days, max_words_in_summary):
    """
    Loop through entailments collections and add an explanation of difference if entailment : contradiction
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    qna_flags_collection = connection.get_collection("entailments")
    similar_questions_collection = connection.get_collection("similar_questions")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    qna_flags_cursor = qna_flags_collection.find({
        "entailment": "contradiction",
        "Rationale": {"$exists": False}
    }, no_cursor_timeout=True)

    for qna_flag in qna_flags_cursor:
        qnaId = qna_flag["qnaId"]
        # Get Similar questions from the `similar_questions` collection for this qnaId
        similar_questions = similar_questions_collection.find_one({"qnaId": qnaId})

        question = qna_flag["question"]
        answer = qna_flag["answer"]
        date = qna_flag["date"]

        if date < cutoff_date_start or date > cutoff_date_end:
            continue

        if similar_questions:
            question_speaker = similar_questions.get("question_speaker", "")
            answer_speaker = similar_questions.get("answer_speaker", "")
            # continue if Rationale already exists in entailments collection
            if "Rationale" in qna_flag:
                logger.info("Rationale already exists")
                continue
            similar_qna_faiss = similar_questions.get("related_qna_faiss", [])
            # Create the prompt
            prompt = get_generate_summary_prompt(question_speaker, question, answer_speaker, answer, similar_qna_faiss, max_words_in_summary)
            # Ask OpenAI to summarize the differences
            summary = openai_service.get_completion(prompt)
            logger.info(f"LLM rationale for qnaId {qnaId} added")
            # Add the summary into entailments collection against the same qnaId
            qna_flags_collection.update_one(
                {"qnaId": qnaId},
                {"$set": {"Rationale": summary, "updated_at": datetime.now()}}
            )
