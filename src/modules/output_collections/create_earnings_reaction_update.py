from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


def create_earnings_reaction_update(cutoff_date, lookback_days):
    # find all earnings events in public_investor_event_outputs for the given date range, for those tickers extract stock price from stock_prices for the day before and the day after and calculate returns
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    stock_prices_collection = connection.get_stock_collection("stock_prices")

    if isinstance(cutoff_date, str):
        cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    else:
        cutoff_date_end = cutoff_date

    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    start_date = cutoff_date_start
    end_date = cutoff_date_end
    events = public_investor_events_outputs_collection.find({
        "event_type": "earnings",
        "date": {"$gte": start_date, "$lte": end_date}
    }, no_cursor_timeout=True)

    for event in events:
        event_id = event["event_id"]
        ticker = event["ticker"]
        event_date = event["date"]
        event_date = event_date.replace(hour=0, minute=0, second=0, microsecond=0)
        logger.info(f"Calculating earnings reaction for event with ID {event_id} and {ticker} on date {event_date}.")

        # Get the stock prices for the ticker on the day before and the day after the earnings event
        date_before = event_date - timedelta(days=1)
        date_after = event_date + timedelta(days=1)

        stock_price_before = stock_prices_collection.find_one({"ticker": ticker, "date": date_before})
        stock_price_after = stock_prices_collection.find_one({"ticker": ticker, "date": date_after})

        if not stock_price_before:
            date_before_2d = event_date - timedelta(days=2)
            stock_price_before = stock_prices_collection.find_one({"ticker": ticker, "date": date_before_2d})
            if not stock_price_before:
                date_before_3d = event_date - timedelta(days=3)
                stock_price_before = stock_prices_collection.find_one({"ticker": ticker, "date": date_before_3d})
                if not stock_price_before:
                    sentiment = "TBD"
                    public_investor_events_outputs_collection.update_one(
                        {"event_id": event_id},
                        {
                            "$set": {
                                "sentiment": sentiment
                            }
                        },
                        upsert=True
                    )
                    continue

        if not stock_price_after:
            date_after_2d = event_date + timedelta(days=2)
            stock_price_after = stock_prices_collection.find_one({"ticker": ticker, "date": date_after_2d})

            if not stock_price_after:
                sentiment = "TBD"
                public_investor_events_outputs_collection.update_one(
                    {"event_id": event_id},
                    {
                        "$set": {
                            "sentiment": sentiment
                        }
                    },
                    upsert=True
                )

                continue

        # Calculate the earnings reaction
        earnings_reaction = (stock_price_after["closingPrice"] - stock_price_before["closingPrice"]) / stock_price_before["closingPrice"]
        logger.info(f"Earnings reaction for {ticker} on {event_date} is {earnings_reaction:.2%}.")
        if earnings_reaction > 0:
            sentiment = "stockUP"
        else:
            sentiment = "stockDOWN"

        # Store the earnings reaction in the database public_investor_events_outputs as upsert under sentiment
        public_investor_events_outputs_collection.update_one(
            {"event_id": event_id},
            {
                "$set": {
                    "sentiment": sentiment,
                    "earnings_reaction": earnings_reaction
                }
            },
            upsert=True
        )
