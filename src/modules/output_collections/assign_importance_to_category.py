from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


def assign_importance_to_category(cutoff_date, lookback_days):
    logger.info("Executing assign_importance_to_category")
    connection = DatabaseFactory().get_mongo_connection()
    categories_collection = connection.get_collection("sector_categories")
    qna_importance = connection.get_collection("qna_importance")
    companies_collection = connection.get_collection("companies")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    # Load all sector_categories once and cache them
    logger.info("Loading sector categories...")
    sector_categories_data = {}
    for category_doc in categories_collection.find():
        key = (category_doc["sector"], category_doc["category"])
        sector_categories_data[key] = category_doc
    logger.info(f"Loaded {len(sector_categories_data)} sector categories")

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "ticker": {"$in": tickers},
        "is_importance_assigned": False
    }

    with qna_importance.find(query, no_cursor_timeout=True).batch_size(200) as qna_cursor:
        for qna in qna_cursor:
            qnaId = qna["qnaId"]
            logger.info(f"Assigning importance to {qnaId}")
            sector = qna["sector"]
            category = qna["category"]
            if sector == "consumer":
                sector = "consumer_disc"

            # Use cached sector_categories instead of querying database
            sector_category = sector_categories_data.get((sector, category))
            if sector_category:
                category_importance = sector_category["importance"]
            else:
                logger.info(f"Missing 'Category' or 'Importance' columns in sector_topics for sector: {sector}")
                category_importance = "Not Important"

            # Store the category back into MongoDB with upsert
            qna_importance.update_one(
                {"qnaId": qnaId},
                {"$set": {
                    "importance": category_importance,
                    "sector": sector,
                    "is_importance_assigned": True,
                    "updated_at": datetime.now()
                }},
                upsert=True
            )
            logger.info(f"Category: {category}, Category Importance: {category_importance}")


if __name__ == "__main__":
    assign_importance_to_category("2025-06-03", 1780)
