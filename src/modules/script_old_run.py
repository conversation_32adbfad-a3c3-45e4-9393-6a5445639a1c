from datetime import datetime, timedelta

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory

from src.modules.input_collections.qna import process_investor_events_for_qna
from src.modules.input_collections.upload_sp_events_to_s3 import upload_sp_events_to_s3

from src.modules.process_collections.create_embeddings import create_embeddings_collection
from src.modules.process_collections.create_entailment_collection import create_entailment_collection
from src.modules.process_collections.create_llm_answer_collection import create_llm_answer_collection
from src.modules.process_collections.create_similar_questions import create_similar_questions_collection
from src.modules.process_collections.create_similarity_collection import create_similarity_collection
from src.modules.process_collections.create_similarity_mda_collection import create_similarity_mda_collection

from src.modules.output_collections.add_llm_rationale_to_entailment import add_LLM_rationale_to_entailment
from src.modules.output_collections.create_trend_collection import create_trend_collection
from src.modules.output_collections.create_insights_collection import create_insights_collection
from src.modules.output_collections.create_category_collection import create_category_collection
from src.modules.output_collections.assign_importance_to_category import assign_importance_to_category
from src.modules.output_collections.create_qna_flags_collection import create_qna_flags_collection
from src.modules.output_collections.create_output_qna_collection import create_output_qna_collection
from src.modules.output_collections.create_event_output_collection import create_event_output_collection
from src.modules.output_collections.create_earnings_anomalies_collection import create_earnings_anomalies_collection
from src.modules.output_collections.create_earnings_message import create_earnings_message
from src.modules.output_collections.create_mda_outputs import create_mda_outputs
from src.modules.output_collections.create_event_summary import create_event_summary
from src.modules.output_collections.create_insights_mda_collection import create_insights_mda_collection
from src.modules.output_collections.create_trend_mda_collection import create_mda_trend_collection
from src.modules.output_collections.rank_insights import rank_insights

# Not necessarily part of output collection
from src.modules.output_collections.create_email_collection import create_email_collection
from src.modules.output_collections.create_earnings_dates_collection import create_earnings_dates_collection
from src.modules.output_collections.create_earnings_reaction_update import create_earnings_reaction_update


logger = get_logger(__name__)

LOOKBACK_DAYS = 1000


def update_input_collections(cutoff_date):
    logger.info("create_public_investor_events_collection done")
    # process_investor_events_for_qna(cutoff_date, LOOKBACK_DAYS)
    logger.info("create_qnas_collection done")
    # upload_sp_events_to_s3(cutoff_date, LOOKBACK_DAYS)
    logger.info("create_s3_bucket done")


def update_process_collections(cutoff_date, LOOKBACK_DAYS):  # each of these can be run in parallel
    # create_embeddings_collection(cutoff_date)
    logger.info("create_embeddings_collection done")

    MIN_CHUNK_LENGTH = 10
    ANALYSIS_WINDOW_DAYS = 180
    # create_similarity_mda_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, min_chunk_length=MIN_CHUNK_LENGTH, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    logger.info("Completed create_similarity_mda_collection")

    ANALYSIS_WINDOW_DAYS = 180  # Replace with your desired analysis window
    MIN_SIMILARITY = 0.4  # Replace with your desired minimum similarity threshold
    TOPN = 6  # Replace with your desired number of top similar questions
    MIN_QUESTION_LENGTH = 20  # in characters
    MIN_ANSWER_LENGTH = 20  # in characters
    # create_similar_questions_collection(ANALYSIS_WINDOW_DAYS, MIN_SIMILARITY, TOPN, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH, cutoff_date, LOOKBACK_DAYS)
    logger.info("similar Questions collection created")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 50
    # create_entailment_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH)
    logger.info("entailments collection created")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 50

    # create_category_collection(cutoff_date, LOOKBACK_DAYS)

    # create_similarity_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH, ANALYSIS_WINDOW_DAYS)
    logger.info("create_similarity_collection done")

    MIN_QUESTION_LENGTH = 10
    MIN_ANSWER_LENGTH = 100

    # create_llm_answer_collection(cutoff_date, LOOKBACK_DAYS, MIN_QUESTION_LENGTH, MIN_ANSWER_LENGTH)
    logger.info("LLM answers created")


def update_output_collections(cutoff_date, LOOKBACK_DAYS, analysis_window):
    MAX_WORDS_IN_SUMMARY = 50
    # add_LLM_rationale_to_entailment(cutoff_date, LOOKBACK_DAYS, MAX_WORDS_IN_SUMMARY)
    logger.info("LLM rationale added to entailments collection done")

    MAX_WORDS = 50
    ANALYSIS_WINDOW_DAYS = 180
    # create_mda_trend_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, max_words=MAX_WORDS, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    logger.info("Completed create_mda_trend_collection")

    MAX_WORDS = 50
    # create_trend_collection(cutoff_date, LOOKBACK_DAYS, MAX_WORDS, analysis_window)
    logger.info("Trend collection created")

    # create_insights_collection(cutoff_date, LOOKBACK_DAYS)
    logger.info("Insights collection created")

    # create_insights_mda_collection(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS)
    logger.info("Completed create_insights_mda_collection")

    # CHANGE: This was the original flow
    # create_category_collection(cutoff_date, LOOKBACK_DAYS)
    logger.info("Questions categorized and importance assigned")

    assign_importance_to_category(cutoff_date, LOOKBACK_DAYS)

    LLM_similarity_threshold = 0.6
    entailment_probability_contradiction_threshold = 0.5
    similar_questions_faiss_threshold = 0.65
    create_qna_flags_collection(cutoff_date, LOOKBACK_DAYS, LLM_similarity_threshold, entailment_probability_contradiction_threshold, similar_questions_faiss_threshold)
    logger.info("create_qna_flags_collection done")

    MIN_ANSWER_LENGTH = 30  # in characters

    create_output_qna_collection(cutoff_date, LOOKBACK_DAYS, MIN_ANSWER_LENGTH)
    logger.info("Final QNA collection created")

    create_event_output_collection(cutoff_date, LOOKBACK_DAYS)
    logger.info("Event output collection created")

    create_earnings_anomalies_collection(cutoff_date, LOOKBACK_DAYS)
    create_earnings_message(cutoff_date, LOOKBACK_DAYS)
    ANALYSIS_WINDOW_DAYS = 180
    create_mda_outputs(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS, analysis_window_days=ANALYSIS_WINDOW_DAYS)
    create_event_summary(cutoff_date, LOOKBACK_DAYS, ANALYSIS_WINDOW_DAYS)
    logger.info("Event summaries updated")
    rank_insights(cutoff_date=cutoff_date, lookback_days=LOOKBACK_DAYS)
    logger.info("Insights ranked")

    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    companies_collection.update_many({}, {'$set': {'select_for_processing': 0}})
    logger.info("Set select_for_processing to 0 for all companies")
    # create_tenk_insights(cutoff_date, LOOKBACK_DAYS)


def updatecollections():
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

    update_input_collections(cutoff_date)

    analysis_window = 182
    update_process_collections(cutoff_date, LOOKBACK_DAYS)

    update_output_collections(cutoff_date, LOOKBACK_DAYS, analysis_window)

    create_earnings_dates_collection()

    date_email = (datetime.now() + timedelta(days=-1)).strftime("%Y-%m-%d")
    # create_email_collection(date_email)
    create_earnings_reaction_update(cutoff_date, LOOKBACK_DAYS)

    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    current_datetime = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    filename = f"output_log_{current_datetime}.txt"
    with open(filename, "w") as file:
        file.write("code run complete")
        # Count the number of records updated in public_investor_events_outputs collection in the last 2 hours
        two_hours_ago = datetime.now() - timedelta(hours=2)
        count = public_investor_events_outputs_collection.count_documents({"updated_at": {"$gte": two_hours_ago}})
        file.write(f"\nNumber of records updated in public_investor_events_outputs collection in the last 2 hours: {count}")
    logger.info(f"Log file created: {filename}")
    return
