import json
from typing import Optional, Dict, Any
from src.services.bedrock import BedrockManager
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.scripts.company_tracker import preprocess


def get_company_matching_prompt(company_name, filtered_companies):
    prompt = """You are a precise matching tool. Your task is to determine if a given company name exists in a reference list of companies, and if so, return the exact company name as it appears in the reference list.

Input:
1. Company Name: {company_name}
2. Reference List: {reference_list}

Instructions:
- Compare the given company name against the reference list.
- The matching should be case-insensitive.
- If the company name matches any company in the reference list, return ONLY the company name exactly as it appears in the reference list.
- If there is no match, return nothing (empty response).
- Do not return partial matches.
- Do not return any explanations, just the exact company name if found.

Example:
Company Name: apple
Reference List: [{{"company": "Apple Inc.", "ticker": "AAPL"}}, {{"company": "Microsoft", "ticker": "MSFT"}}]
Expected Output: Apple Inc.
"""
    reference_list = [{"company": doc["company_name"], "ticker": doc["ticker"]} for doc in filtered_companies]
    company_matching_prompt = prompt.format(company_name=company_name, reference_list=json.dumps(reference_list))
    return company_matching_prompt


def find_company_in_mongodb(name: str, similarity_threshold: float = 0.75) -> Optional[Dict[str, Any]]:
    """
    Find a company in MongoDB collection based on name matching.
    Returns the matching company document or None if no match found.
    """

    connection = DatabaseFactory().get_mongo_connection()
    bedrock_manager = BedrockManager()
    company_tracker_collection = connection.get_podcast_collection("company_tracker")
    proc_name = preprocess(name)

    company = company_tracker_collection.find_one(
        {
            "$or": [
                {"company_name": name},
                {"preprocessed_company_name": name},
                {"preprocessed_company_name": proc_name},
                {"ticker": name},
            ],
            "is_active": True,
            "is_rejected": False
        }
    )
    if company:
        return company

    company = company_tracker_collection.find_one({"is_active": True, "is_rejected": False, "synonyms": name.lower().strip()})
    if company:
        return company

        # Try embedding similarity
    if proc_name is None or proc_name.strip() == "":
        return None
    query_embedding = bedrock_manager.get_embeddings(proc_name)

    pipeline = [
        {
            "$vectorSearch": {
                "queryVector": query_embedding,
                "path": "embedding",
                "numCandidates": 100,
                "limit": 10,
                "index": "default",
            },
        },
        {"$match": {"is_active": True, "is_rejected": False}},
        {"$addFields": {"score": {"$meta": "vectorSearchScore"}}},
    ]

    matched_companies = list(company_tracker_collection.aggregate(pipeline))
    filtered_matched_companies = [
        doc
        for doc in matched_companies
        if doc.get("score", 0) >= similarity_threshold
    ]
    if len(filtered_matched_companies):
        company_matching_prompt = get_company_matching_prompt(company_name=name, filtered_companies=filtered_matched_companies)
        openai_service = OpenAIService()
        company_name = openai_service.get_completion_without_limits(
            prompt=company_matching_prompt,
            model="gpt-4o-mini",
            temperature=0)
        for _company in filtered_matched_companies:
            if _company["company_name"] == company_name:
                company_tracker_collection.update_one(
                    {"_id": _company["_id"]},
                    {"$push": {"synonyms": proc_name}}
                )
                return _company
    return None


if __name__ == "__main__":
    result = find_company_in_mongodb("cae incorporation")
    print(result)
