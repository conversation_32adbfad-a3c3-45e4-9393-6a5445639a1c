import os
import tempfile
import requests
from src.services.s3 import S3Client
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.core.constants import S3_PODCASTS_BUCKET_NAME
from src.podcasts.process_status import ProcessStatus, ProcessStep


logger = get_logger(__name__)


def download_audio_file(url):
    # Add headers if required (User-Agent is often helpful)
    headers = {
        "User-Agent": "Mozilla/5.0 (compatible; AudioDownloader/1.0)"
    }
    # Create a named temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
    try:
        with requests.get(url, headers=headers, stream=True, timeout=30) as response:
            response.raise_for_status()
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    temp_file.write(chunk)
        temp_file.close()
        return temp_file.name  # Return the temp file's path
    except Exception as ex:
        temp_file.close()
        os.unlink(temp_file.name)
        raise ex


def upload_podcast_to_s3():
    connection = DatabaseFactory().get_mongo_connection()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    s3_client = S3Client()
    with podcast_events_final_collection.find({"next_step": ProcessStep.UPLOAD_TO_S3.value}, no_cursor_timeout=True).batch_size(100) as podcast_cursor:
        for podcast in podcast_cursor:
            # Remove this once we have all the data
            if 's3_uri_transcript' in podcast:
                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.PROCESSING.value,
                            "completed_step": ProcessStep.UPLOAD_TO_S3.value,
                            "next_step": ProcessStep.TRANSCRIBE.value,
                        }
                    },
                )
                continue
            audio_link = podcast["audio_link"]
            if audio_link:
                local_file_path = download_audio_file(audio_link)
                s3_key = f"podcasts/{podcast['podcast_id']}/{podcast['listennotes_id']}.mp3"
                s3_client.upload_file(file_path=local_file_path, s3_key=s3_key, bucket_name=S3_PODCASTS_BUCKET_NAME)
                os.remove(local_file_path)
                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.PROCESSING.value,
                            "completed_step": ProcessStep.UPLOAD_TO_S3.value,
                            "next_step": ProcessStep.TRANSCRIBE.value,
                            "s3_uri_transcript": f"s3://{S3_PODCASTS_BUCKET_NAME}/{s3_key}",
                        }
                    },
                )
                logger.info(f"Uploaded podcast episode: {podcast['episode_title']} to S3")
            else:
                logger.warning(f"No audio link found for podcast {podcast['episode_title']}")


if __name__ == "__main__":
    upload_podcast_to_s3()
