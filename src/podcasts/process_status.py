from enum import Enum


class ProcessStatus(Enum):
    PROCESSING = "PROCESSING"
    MOVE_TO_ADMIN = "MOVE_TO_ADMIN"
    DO_NOT_PROCESS = "DO_NOT_PROCESS"
    COMPLETED = "COMPLETED"
    ERROR = "ERROR"


class ProcessStep(Enum):
    TRANSCRIBE = "TRANSCRIBE"
    DETECT_SPEAKERS = "DETECT_SPEAKERS"
    TAG_SPEAKERS = "TAG_SPEAKERS"
    UPLOAD_TO_S3 = "UPLOAD_TO_S3"
    CHUNK_TRANSCRIPTS = "CHUNK_TRANSCRIPTS"
    GATHER_INSIGHTS = "GATHER_INSIGHTS"
    SUMMARIZE_TRANSCRIPTS = "SUMMARIZE_TRANSCRIPTS"
