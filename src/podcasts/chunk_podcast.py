from concurrent.futures import Thread<PERSON>oolExecutor

from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.gemini_service import GeminiService
from src.podcasts.process_status import ProcessStatus, ProcessStep
from src.podcasts.find_companies import find_company_in_mongodb
from src.podcasts.parse_json_from_markdown import parse_json_from_markdown

logger = get_logger(__name__)


def get_sectors(sector):
    connection = DatabaseFactory().get_mongo_connection()
    sector_categories_collection = connection.get_collection("sector_categories")
    sector_categories = list(sector_categories_collection.find({"sector": sector}))
    categories = [category["category"] for category in sector_categories]
    categories.append("others")
    return categories


def get_transcript_chunking_prompt(podcast_transcript, sector):
    categories = get_sectors(sector)
    categories_str = "\n".join([f"- {category}" for category in categories])
    transcript_chunking_prompt = f"""## Task Description
You will be given a podcast transcript that needs to be divided into smaller, contextual chunks for easier processing and analysis. Each chunk should represent a coherent segment of conversation focused on a specific topic, question-answer pair, or theme.

## Chunking Instructions
1. Identify natural topic transitions in the conversation (often indicated by a new question, speaker shift, or clear topic change).
2. Create separate chunks for:
   - Introduction/opening segment
   - Each major topic or question-answer exchange
   - Conclusion/closing segment
3. Keep each chunk complete enough to maintain context and meaning.
4. Aim for chunks of roughly similar length (approximately 200-800 words), but prioritize topic coherence over exact length.
5. Preserve all speaker attributions and the original dialogue format exactly as in the transcript (no rewording or summarization).
6. For each chunk, **assign the most appropriate category** from the following list based on the content of that chunk:
   {categories_str}

## Categorization Instructions
- Select exactly one category per chunk that best fits the main theme or subject matter discussed.
- If a chunk contains multiple themes, select the category that most closely represents the primary focus.
- Do not invent or infer information not explicitly present in the transcript.
- Use the transcript’s exact wording to determine the category-do not paraphrase or speculate.

## Output Format
Return a JSON array, where each element is an object with the following fields:

{{
  "category": "One category from the provided categories list",
  "transcript_text": "The verbatim conversation text for this chunk with speaker attributions preserved"
}}

## Transcript to chunk and categorize
{podcast_transcript}
"""
    return transcript_chunking_prompt


def get_tracked_orgs_mentioned(entities):
    tracked_orgs_dict = {}

    def process_entity(entity):
        company = find_company_in_mongodb(entity)
        if company:
            return (entity, {
                "company_name": company["company_name"],
                "ticker": company["ticker"]
            })
        else:
            return (entity, None)

    with ThreadPoolExecutor() as executor:
        results = executor.map(process_entity, entities)

    for entity, company_data in results:
        tracked_orgs_dict[entity] = company_data

    return tracked_orgs_dict


def chunk_podcast_transcript():
    connection = DatabaseFactory().get_mongo_connection()
    gemini_service = GeminiService()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")

    with podcast_events_final_collection.find({"next_step": ProcessStep.CHUNK_TRANSCRIPTS.value}, no_cursor_timeout=True).batch_size(20) as podcast_cursor:
        for podcast in podcast_cursor:
            try:
                podcast_transcript = podcast["transcript"]
                entities = podcast["org_entities"]
                sector = podcast["sector"] if podcast["sector"] else "software"
                transcript_chunking_prompt = get_transcript_chunking_prompt(podcast_transcript=podcast_transcript, sector=sector)

                response = gemini_service.get_completion_without_limits(prompt=transcript_chunking_prompt, temperature=0, response_format={"type": "json_object"})
                chunked_transcripts = parse_json_from_markdown(response)

                tracked_org_dict = get_tracked_orgs_mentioned(podcast["org_entities"])
                tracked_tickers = [tracked_org_dict[entity]["ticker"] for entity in podcast["org_entities"] if tracked_org_dict[entity]]
                tracked_tickers = list(set(tracked_tickers))
                tracked_companies = [tracked_org_dict[entity]["company_name"] for entity in podcast["org_entities"] if tracked_org_dict[entity]]
                tracked_companies = list(set(tracked_companies))

                for chunk in chunked_transcripts:
                    for entity in entities:
                        if "entities" not in chunk.keys():
                            chunk["entities"] = []
                        if entity in chunk["transcript_text"]:
                            chunk["entities"].append(entity)

                chunks_by_category = {}
                for chunk in chunked_transcripts:
                    if chunk["category"] not in chunks_by_category.keys():
                        chunks_by_category[chunk["category"]] = {
                            "entities": [],
                            "transcript_text": ""
                        }

                    chunks_by_category[chunk["category"]]["transcript_text"] += chunk["transcript_text"] + "\n"
                    chunks_by_category[chunk["category"]]["entities"].extend(chunk["entities"])

                final_chunks = []
                for category, transcript in chunks_by_category.items():
                    transcript_tracked_tickers = [tracked_org_dict[entity]["ticker"] for entity in transcript["entities"] if tracked_org_dict[entity]]
                    transcript_tracked_tickers = list(set(transcript_tracked_tickers))
                    transcript_tracked_companies = [tracked_org_dict[entity]["company_name"] for entity in transcript["entities"] if tracked_org_dict[entity]]
                    transcript_tracked_companies = list(set(transcript_tracked_companies))

                    final_chunks.append({
                        "category": category,
                        "transcript": transcript["transcript_text"],
                        "entities": transcript["entities"],
                        "tracked_companies": transcript_tracked_companies,
                        "tracked_tickers": transcript_tracked_tickers
                    })

                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.PROCESSING.value,
                            "completed_step": ProcessStep.CHUNK_TRANSCRIPTS.value,
                            "next_step": ProcessStep.GATHER_INSIGHTS.value,
                            "chunked_transcripts": final_chunks,
                            "tracked_companies": tracked_companies,
                            "tracked_tickers": tracked_tickers
                        }
                    }
                )

                logger.info(f"Chunked podcast episode: {podcast['episode_title']}")
            except Exception as e:
                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.ERROR.value,
                            "next_step": ProcessStatus.MOVE_TO_ADMIN.value,
                            "reason": f"Error in Chunking podcast, Exception: {e}"
                        }
                    }
                )
                logger.exception(f"Couldnt chunk podcast episode: {podcast['episode_title']}")


if __name__ == "__main__":
    chunk_podcast_transcript()
