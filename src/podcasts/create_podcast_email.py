from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory


def create_podcast_emails(CUTOFF_DATE, LOOKBACK_DAYS):
    connection = DatabaseFactory().get_mongo_connection()
    podcast_emails_collection = connection.get_email_collection("podcast_emails")
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")

    # get all podcasts that have summary between cutoff_date and lookback_days

    query = {
        "date": {"$gte": CUTOFF_DATE - timedelta(days=LOOKBACK_DAYS), "$lte": CUTOFF_DATE},
        "status": "COMPLETED"
    }

    podcasts = list(podcast_events_final_collection.find(query))

    if not podcasts:
        print("No podcasts found in the given date range.")
        return

    for podcast in podcasts:
        if podcast_emails_collection.find_one({"listennotes_id": podcast["listennotes_id"]}):
            continue
        email = {
            "is_approved": 0,
            "is_delivered": 0,
            "is_summary_approved": 0,
            "listennotes_id": podcast["listennotes_id"],
            "date_new": podcast["date"],
            "updated_at": datetime.now(),
            "new_version": 1
        }
        podcast_emails_collection.insert_one(email)
        print(f"Inserted email for podcast ID: {podcast['listennotes_id']}")
    print("Emails inserted successfully")


if __name__ == "__main__":
    create_podcast_emails(CUTOFF_DATE=datetime.now(), LOOKBACK_DAYS=10)
