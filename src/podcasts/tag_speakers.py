from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep
from src.podcasts.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_speaker_tagging_prompt(podcast_title, podcast_transcript: str, raw_speakers_list, speakers):
    lines = podcast_transcript.splitlines()
    first_30_percent = max(1, int(len(lines) * 0.3))
    relevant_podcast_transcript = '\n'.join(lines[:first_30_percent])

    speaker_information = ""
    speaker_id_list = ",".join(raw_speakers_list)

    for speaker in speakers:
        speaker_information += "<speaker>\n"
        speaker_information += f"- Actual Name: {speaker['name']}\n"
        speaker_information += f"- Company: {speaker['company']}\n"
        speaker_information += f"- Role: {speaker['role']}\n"
        speaker_information += f"- Speaker Type: {speaker['speaker_type']}\n"
        speaker_information += "</speaker>\n"

    speaker_tagging_prompt = f"""You are an expert transcript analyst specializing in podcast speaker identification. Your task is to create an accurate JSON mapping between speaker IDs (speaker_0, speaker_1, etc.) and their actual names from a podcast transcript.

## Input Information:
1. Podcast Title: The tile of the podcast
2. Podcast Transcript: A transcript with {len(raw_speakers_list)} speakers tagged as {speaker_id_list}
3. Speaker Information: A list containing actual names, companies, roles, and speaker types (host/guest)

## Your Task:
Analyze the transcript carefully and match each speaker_ID to their actual name by following these steps:

1. Identify contextual clues in the transcript (introductions, self-references, topics discussed)
2. Compare speaking patterns with the provided speaker information
3. Pay special attention to:
- Introduction segments where hosts typically introduce themselves and guests
- References to professional roles or experiences that match the provided speaker information
- Dialogue patterns that indicate host vs. guest dynamics


## Accuracy Guidelines:
- If the actual name of the speaker is missing try to use the transcript in the context to find out the speaker names.
- If you're uncertain about a match, provide your best assessment based on contextual evidence
- Ensure all speaker_IDs in the transcript are accounted for in your mapping
- Verify that all actual names from the speaker information list are included
- You must tag and return all the speakers. There are a total of {len(raw_speakers_list)} speakers in the podcast.


Please analyze the following podcast information and provide the speaker ID to actual name mapping:

Podcast Title: {podcast_title}

Transcript:
{relevant_podcast_transcript}

Speaker Information:
{speaker_information}


## Output Format:
Your output must be a valid json without any explanation
[
{{
    "speaker_id": "speaker_0",
    "actual_name": "Actual Name of the speaker"
}},
{{
    "speaker_id": "speaker_1",
    "actual_name": "Actual Name of speaker"
}}
]

Now identify who are {speaker_id_list}.
"""
    return speaker_tagging_prompt


def tag_speakers():
    openi_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    with podcast_events_final_collection.find({"next_step": ProcessStep.TAG_SPEAKERS.value}, no_cursor_timeout=True).batch_size(100) as podcast_cursor:
        for podcast in podcast_cursor:
            podcast_title = podcast["episode_title"]
            podcast_transcript = podcast["raw_transcript"]
            raw_speakers_list = podcast["raw_speakers_list"]
            speakers = podcast["speakers"]
            speaker_tagging_prompt = get_speaker_tagging_prompt(podcast_title=podcast_title, podcast_transcript=podcast_transcript, raw_speakers_list=raw_speakers_list, speakers=speakers)
            response = openi_service.get_completion_without_limits(prompt=speaker_tagging_prompt, temperature=0)
            speaker_tags = parse_json_from_markdown(response)
            tagged_podcast_transcript = podcast_transcript
            for speaker_tag in speaker_tags:
                tagged_podcast_transcript = tagged_podcast_transcript.replace(speaker_tag["speaker_id"], speaker_tag["actual_name"])

            podcast_events_final_collection.update_one(
                {"_id": podcast["_id"]},
                {
                    "$set": {
                        "status": ProcessStatus.PROCESSING.value,
                        "completed_step": ProcessStep.TAG_SPEAKERS.value,
                        "next_step": ProcessStep.CHUNK_TRANSCRIPTS.value,
                        "transcript": tagged_podcast_transcript,
                    }
                }
            )
            logger.info(f"Completed tagging speakers for: {podcast['episode_title']}")


if __name__ == "__main__":
    tag_speakers()
