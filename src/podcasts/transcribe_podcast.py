import json
from src.services.s3 import S3Client
from src.core.logging import get_logger
from src.services.deepgram_transcription import transcribe
from src.database.factory import DatabaseFactory
from src.core.constants import S3_PODCASTS_BUCKET_NAME
from src.podcasts.process_status import ProcessStatus, ProcessStep


logger = get_logger(__name__)


def create_conversation(json_data):
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    conversation = []
    current_speaker = None
    current_text = []
    unique_speakers = set()

    for word_obj in data:
        speaker_id = word_obj["speaker"]
        unique_speakers.add(speaker_id)

        if current_speaker is not None and speaker_id != current_speaker:
            text = " ".join(current_text)
            conversation.append(f"*speaker_{current_speaker}:* {text}")
            current_text = []

        current_speaker = speaker_id
        word = word_obj.get("punctuated_word", word_obj["word"])
        current_text.append(word)

    if current_text:
        text = " ".join(current_text)
        conversation.append(f"*speaker_{current_speaker}:* {text}")

    speakers_list = [f"speaker_{id}" for id in sorted(list(unique_speakers))]

    return conversation, speakers_list


def transcribe_podcast():
    connection = DatabaseFactory().get_mongo_connection()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    with podcast_events_final_collection.find({"next_step": ProcessStep.TRANSCRIBE.value}, no_cursor_timeout=True).batch_size(50) as podcast_cursor:
        for podcast in podcast_cursor:
            if 'raw_transcript' in podcast and "raw_speakers_list" in podcast and "org_entities" in podcast:
                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.PROCESSING.value,
                            "completed_step": ProcessStep.TRANSCRIBE.value,
                            "next_step": ProcessStep.TAG_SPEAKERS.value,
                        }
                    },
                )
                continue
            s3_uri_transcript = podcast["s3_uri_transcript"]
            s3_key = s3_uri_transcript.replace(f"s3://{S3_PODCASTS_BUCKET_NAME}/", "")
            s3_client = S3Client()
            try:
                s3_client.head_object(s3_key=s3_key, bucket_name=S3_PODCASTS_BUCKET_NAME)
            except s3_client.exceptions.ClientError as e:
                if e.response["Error"]["Code"] == "404":
                    logger.exception(f"File '{s3_uri_transcript}' does not exist in. Skipping.")
                    continue
                else:
                    # Some other error; re-raise it
                    raise
            presigned_url = s3_client.generate_presigned_url(
                s3_key=s3_key, bucket_name=S3_PODCASTS_BUCKET_NAME
            )
            transcription = transcribe(presigned_url)

            entity_list = transcription["results"]['channels'][0]['alternatives'][0]["entities"]
            org_entities = [entity["value"] for entity in entity_list if entity['label'] == "ORGANIZATION"]
            org_entities = list(set(org_entities))

            words = transcription["results"]['channels'][0]['alternatives'][0]['words']
            podcast_transcript, raw_speakers_list = create_conversation(words)
            podcast_transcript_str = "\n".join(podcast_transcript)

            podcast_events_final_collection.update_one(
                {"_id": podcast["_id"]},
                {
                    "$set": {
                        "status": ProcessStatus.PROCESSING.value,
                        "completed_step": ProcessStep.TRANSCRIBE.value,
                        "next_step": ProcessStep.TAG_SPEAKERS.value,
                        "raw_transcript": podcast_transcript_str,
                        "raw_speakers_list": raw_speakers_list,
                        "org_entities": org_entities,
                    }
                },
            )
            logger.info(f"Completed transcripting podcast episode {podcast['episode_title']}")


if __name__ == "__main__":
    transcribe_podcast()
