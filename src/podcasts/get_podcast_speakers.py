import re
from markdownify import markdownify as md
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep
from src.podcasts.scripts.company_tracker import preprocess
from src.podcasts.find_companies import find_company_in_mongodb
from src.podcasts.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def strip_markup_links(text: str) -> str:
    """
    Remove Markdown links (keep link text) and HTML tags.
    """
    text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)
    text = re.sub(r'<[^>]+>', '', text)
    return text


def normalize_whitespace(text: str) -> str:
    """
    Collapse multiple spaces into one space,
    collapse multiple newlines into a single newline, and trim lines
    while preserving paragraph breaks.
    """
    # Normalize spaces (but keep newlines untouched)
    text = re.sub(r'[ \t]+', ' ', text)

    # Normalize newlines: collapse 2+ newlines into 2 (for paragraph breaks)
    text = re.sub(r'\n\s*\n+', '\n\n', text)

    # Trim each line
    lines = [line.strip() for line in text.strip().split('\n')]
    return '\n'.join(lines)


def preprocess_podcast_metadata(podcast_title: str, raw_title: str, raw_description: str) -> dict:
    """
    Full pipeline: clean markup, normalize whitespace, and segment description.
    Returns a dict with cleaned title and description.
    """
    podcast_title = normalize_whitespace(strip_markup_links(podcast_title))
    title = normalize_whitespace(strip_markup_links(raw_title))
    description = md(raw_description)
    description = normalize_whitespace(strip_markup_links(description))
    return {
        "podcast_title": podcast_title,
        "title": title,
        "description": description,
    }


def get_speaker_extraction_prompt(podcast_title, episode_title, podcast_description):
    speaker_extraction_prompt = """You are a specialized extraction assistant designed to identify speakers from podcast metadata. Your task is to analyze podcast titles, episode title, and descriptions and extract structured information about ALL speakers speaking in the current episode of the podcast.

    For each speaker, extract the following information in a consistent JSON format:
    - name: The full name of the speaker
    - company: The organization or company they are associated with
    - role: Their professional title or position at the company
    - speaker_type: Whether they are a "host" or a "guest" on the podcast

    INSTRUCTIONS:
    1. First, carefully read the entire title and description to identify all speakers.
    2. Only include speakers who speaking in the current episode.
    2. For each speaker, methodically search for their associated company and role.
    3. Determine if each person is hosting the podcast or appearing as a guest.
    4. If information is not explicitly stated, use reasonable inference based on context, marking uncertain inferences with "likely" (e.g., "likely CEO").
    5. If any field is completely unknown, use "unknown" as the value.
    6. Present your results as a JSON array where each object represents one speaker.
    7. Review your extraction for accuracy and completeness before finalizing.

    Example output format:
    [
        {{
            "name": "Speaker Name",
            "company": "Company Name",
            "role": "Role Title",
            "speaker_type": "host/guest"
        }},
        {{
            "name": "Another Speaker",
            "company": "Another Company",
            "role": "Another Role",
            "speaker_type": "host/guest"
        }}
    ]


    PODCAST INFORMATION:
    Podcast Title: {podcast_title}
    Episode Title: {episode_title}
    Description: {podcast_description}
    """

    speaker_extraction_prompt = speaker_extraction_prompt.format(podcast_title=podcast_title, episode_title=episode_title, podcast_description=podcast_description)
    return speaker_extraction_prompt


def shall_process(speakers):
    # What if two people are taking from the companies we track
    connection = DatabaseFactory().get_mongo_connection()
    person_tracker_collection = connection.get_podcast_collection("person_tracker")
    company_tracker_collection = connection.get_podcast_collection("company_tracker")
    process = False
    company = None
    company_obj = None
    for speaker in speakers:
        speaker_name = speaker.get("name")
        speaker_company = speaker.get("company")
        speaker["is_important"] = False
        if speaker_name == "unknown" and speaker_company == "unknown":
            continue
        company = find_company_in_mongodb(speaker_company)
        if not company:
            try:
                if speaker_company == "unknown":
                    continue
                company_tracker_collection.insert_one({
                    "company_name": speaker_company,
                    "preprocessed_company_name": None,
                    "embedding": None,
                    "synonyms": [],
                    "company_id": None,
                    "is_active": False,
                    "ticker": None,
                    "is_rejected": None
                })
            except Exception as e:
                logger.error(f"Error adding company. Error: {e}")

        if company:
            company_obj = company
            speaker["is_important"] = True
            process = True
        else:
            result = person_tracker_collection.find_one(
                {
                    "name": {'$regex': f'^{speaker_name}$', '$options': 'i'},
                    "company_name": {"$in": [speaker_company, preprocess(speaker_company)]},
                    "is_rejected": False,
                    "is_active": True
                }
            )
            if result:
                speaker["is_important"] = True
                process = True
    return process, company_obj, speakers


def get_podcast_speakers():
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    podcast_events_collection = connection.get_podcast_collection("podcast_events")
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    with podcast_events_collection.find({"is_processed": False}, no_cursor_timeout=True).batch_size(100) as podcast_cursor:
        for podcast in podcast_cursor:
            processed_metadata = preprocess_podcast_metadata(podcast["podcast_title"], podcast["episode_title"], podcast["description"])
            podcast_title = processed_metadata["podcast_title"]
            episode_title = processed_metadata["title"]
            episode_description = processed_metadata["description"]

            speaker_extraction_prompt = get_speaker_extraction_prompt(podcast_title=podcast_title, episode_title=episode_title, podcast_description=episode_description)
            response = openai_service.get_completion_without_limits(prompt=speaker_extraction_prompt, temperature=0)
            speakers = parse_json_from_markdown(response)
            insert_object = {
                "podcast_id": podcast["podcast_id"],
                "podcast_title": podcast["podcast_title"],
                "episode_title": podcast["episode_title"],
                "audio_length_sec": podcast["audio_length_sec"],
                "audio_link": podcast["audio_link"],
                "date": podcast["date"],
                "description": podcast["description"],
                "listennotes_id": podcast["listennotes_id"],
                "podcast_link": podcast["podcast_link"],
                "published_at": podcast["published_at"],
                "updated_at": podcast["updated_at"],
                "speakers": speakers,
                "completed_step": ProcessStep.DETECT_SPEAKERS.value,
            }
            process, company, speakers = shall_process(speakers=speakers)
            insert_object["speakers"] = speakers
            are_all_speakers_unknown = True
            for speaker in speakers:
                if speaker.get("name") != "unknown":
                    are_all_speakers_unknown = False

            if are_all_speakers_unknown:
                process = False

            if process:
                insert_object["status"] = ProcessStatus.PROCESSING.value
                insert_object["next_step"] = ProcessStep.UPLOAD_TO_S3.value
                if company:
                    insert_object["ticker"] = company.get("ticker")
                    insert_object["company_name"] = company["company_name"]
                    insert_object["sector"] = company.get("sector")
                else:
                    insert_object["ticker"] = None
                    insert_object["company_name"] = None
                    insert_object["sector"] = None
            else:
                insert_object["status"] = ProcessStatus.DO_NOT_PROCESS.value
                insert_object["next_step"] = ProcessStatus.MOVE_TO_ADMIN.value
                insert_object["corrected_step"] = ProcessStep.UPLOAD_TO_S3.value
                if are_all_speakers_unknown:
                    insert_object["reason"] = "Couldnt find any speakers from the podcast metadata"
                else:
                    insert_object["reason"] = "Not tracking any of the speakers or companies"
            podcast_events_final_collection.insert_one(insert_object)
            podcast_events_collection.update_one(
                {"_id": podcast["_id"]},
                {
                    "$set": {
                        "is_processed": True
                    }
                }
            )
            logger.info(f"Processed podcast for speakers: {podcast['episode_title']}")


if __name__ == "__main__":
    get_podcast_speakers()
